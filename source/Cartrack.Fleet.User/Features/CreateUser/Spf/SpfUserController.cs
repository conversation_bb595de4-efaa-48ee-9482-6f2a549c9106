﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.CreateUser.Spf;
using Cartrack.Fleet.User.Features.Infratructure;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPost]
    [Route("create")]
    public async Task<ActionResult<SpfCreateUserResponse>> UpdateUserById([FromBody] SpfCreateUserRequest request) {
        var resp = await this._mediator.Send(new SpfCreateUserRequest(request.userId, request.userName, request.email,
            request.cellNumber, request.roleCode, request.serviceType));
        return resp.ToContentResult<SpfCreateUserResponse, ResponseStatus>(this.HttpContext);
    }
}