﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.CreateUser.Spf;

public class SpfCreateUserHandler(IUserRepository repo, ILogger<SpfCreateUserHandler> logger)
    : IRequestHandler<SpfCreateUserRequest, SpfCreateUserResponse> {
    public async Task<SpfCreateUserResponse> <PERSON><PERSON>(SpfCreateUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.userId > 0, "User Id must be greater than zero");
            Requires.NotNullOrEmpty(request.userName, nameof(request.userName));
            Requires.NotNullOrEmpty(request.email, nameof(request.email));
            Requires.NotNullOrEmpty(request.cellNumber, nameof(request.cellNumber));
            Requires.NotNullOrEmpty(request.roleCode, nameof(request.roleCode));
            logger.LogInformation("Retrieving the user with userName={userName}", request.userName);

            var clientUser = await repo.GetUserByUserName(request.userId, request.userName);
            if (clientUser == null) {
                var clientUserRole = await repo.GetUserRoleByCode(request.roleCode);
                if (clientUserRole != null) {
                    var UserObj = new ClientUser() {
                        UserId = request.userId,
                        ClientUserId = Guid.NewGuid().ToString(),
                        UserName = request.userName?.ToLower(),
                        PasswordHash = "XXXXXXXXXX",
                        EMail = request.email,
                        Cts = DateTime.UtcNow,
                        Uts = DateTime.UtcNow,
                        CellNumber = request.cellNumber,
                        ClientUserRoleId = clientUserRole.RoleUuid,
                        ForceRoleSetting = false,
                    };

                    var newClientUser = await repo.CreateUser(UserObj);
                    if (newClientUser != null) {
                        var clientUserStateObj = new ClientUserState {
                            ClientActionId = Guid.NewGuid().ToString(),
                            ClientUserId = newClientUser.ClientUserId,
                            ClientUserStatusId = 10,
                            Cts = DateTime.UtcNow,
                            Uts = DateTime.UtcNow,
                        };
                        var clientUserState = await repo.AddClientUserState(clientUserStateObj);
                        return new SpfCreateUserResponse(new ResponseStatus(UserObj.ClientUserId, true,
                            "User has been created"));
                    }
                    else {
                        var requiresException = new RequiresException("");
                        return new SpfCreateUserResponse(new ResponseStatus(UserObj.ClientUserId, false,
                            "Update skipped: User state doesn't exist"));
                    }
                }
                else {
                    return new SpfCreateUserResponse(new ResponseStatus(null, false,
                        "The client user role doesn't exist"));
                }
            }
            else {
                return new SpfCreateUserResponse(new ResponseStatus(clientUser.ClientUserId, false,
                    "Update skipped: User already exists"));
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfCreateUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfCreateUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}