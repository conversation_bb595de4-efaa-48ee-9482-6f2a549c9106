﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUsersByRole;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("getbyrole")]
    public async Task<ActionResult<SpfGetUserByRoleResponse>> GetUserByRole(long userId, string roleName) {
        var resp = await this._mediator.Send(new SpfGetUserByRoleRequest(userId, roleName));
        return resp.ToContentResult<SpfGetUserByRoleResponse, List<ClientUser>>(this.HttpContext);
    }
}