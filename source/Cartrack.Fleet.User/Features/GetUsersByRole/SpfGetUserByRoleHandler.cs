﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUsersByRole;

public class SpfGetUserByRoleHandler(IUserRepository repo, ILogger<SpfGetUserByRoleHandler> logger)
    : IRequestHandler<SpfGetUserByRoleRequest, SpfGetUserByRoleResponse> {
    public async Task<SpfGetUserByRoleResponse> Handle(SpfGetUserByRoleRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.roleCode, nameof(request.roleCode));
            Requires.IsTrue(() => request.userId > 0, "User Id must be greater than zero");
            logger.LogInformation("[{userId}] Retrieving the user with roleCode={roleCode}", request.userId,
                request.roleCode);

            var user = await repo.GetUserByRole(request.userId, request.roleCode);
            if (user.Any()) {
                return new SpfGetUserByRoleResponse(user);
            }
            else {
                return new SpfGetUserByRoleResponse(null, new Exception("The client user doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetUserByRoleResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetUserByRoleResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}