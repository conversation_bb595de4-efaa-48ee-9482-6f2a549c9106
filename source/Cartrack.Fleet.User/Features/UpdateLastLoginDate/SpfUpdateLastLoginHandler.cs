﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateLastLoginDate;

public class SpfUpdateLastLoginHandler(IUserRepository repo, ILogger<SpfUpdateLastLoginHandler> logger)
    : IRequestHandler<SpfUpdateLastLoginRequest, SpfUpdateLastLoginResponse> {
    public async Task<SpfUpdateLastLoginResponse> Handle(SpfUpdateLastLoginRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.clientUserId, nameof(request.clientUserId));
            //Requires.NotNullOrEmpty(request.lastLoginDate, nameof(request.email));
            logger.LogInformation("[{Agency}] Retrieving the user with clientUserId={clientUserId}", request.userId,
                request.clientUserId);
            ResponseStatus updateStatus = null;
            var clientUser = await repo.GetUserById(request.clientUserId);
            if (clientUser != null) {
                var userLastLoginObj = new ClientLoginHistory {
                    SubClientId = clientUser.ClientUserId,
                    EventTs = request.lastLoginDate.ToUniversalTime(),
                    ClientId = request.userId,
                    LoginSourceId = 60,
                    LoginSourceIp = "0.0.0.0",
                    ClientDetails = "60"
                };
                var clientUserState = await repo.AddUserLastLogin(userLastLoginObj);
                updateStatus = new ResponseStatus(clientUser.ClientUserId, true, "Last login date has been updated");
                return new SpfUpdateLastLoginResponse(updateStatus);
            }
            else {
                updateStatus = new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User account doesn't exist");
                return new SpfUpdateLastLoginResponse(updateStatus);
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfUpdateLastLoginResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfUpdateLastLoginResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}