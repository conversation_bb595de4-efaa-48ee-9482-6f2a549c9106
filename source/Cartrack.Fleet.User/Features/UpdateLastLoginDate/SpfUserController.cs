﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateLastLoginDate;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updatelastlogin")]
    public async Task<ActionResult<SpfUpdateLastLoginResponse>> UpdateLastLoginById(
        [FromBody] SpfUpdateLastLoginRequest request) {
        var resp = await this._mediator.Send(new SpfUpdateLastLoginRequest(request.userId, request.clientUserId,
            request.lastLoginDate));
        return resp.ToContentResult<SpfUpdateLastLoginResponse, ResponseStatus>(this.HttpContext);
    }
}