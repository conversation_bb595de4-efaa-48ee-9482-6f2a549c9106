﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.DeleteUser;
using Cartrack.Fleet.User.Features.Infratructure;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpDelete]
    [Route("deletebyid/{clientUserId}")]
    public async Task<ActionResult<SpfDeleteUserResponse>> DeleteUserById(string clientUserId) {
        var resp = await this._mediator.Send(new SpfDeleteUserRequest(clientUserId));
        return resp.ToContentResult<SpfDeleteUserResponse, ResponseStatus>(this.HttpContext);
    }
}