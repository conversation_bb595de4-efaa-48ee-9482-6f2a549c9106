﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.AddUserDepartment;
using Cartrack.Fleet.User.Features.GetUserById;
using Cartrack.Fleet.User.Features.GetUsers;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.DeleteUser;

public class SpfDeleteUserHandler(IUserRepository repo, ILogger<SpfDeleteUserHandler> logger)
    : IRequestHandler<SpfDeleteUserRequest, SpfDeleteUserResponse> {
    public async Task<SpfDeleteUserResponse> Handle(SpfDeleteUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.userUserId, nameof(request.userUserId));
            logger.LogInformation("Retrieving the user with userUserId={userUserId}", request.userUserId);
            var clientUser = await repo.GetUserById(request.userUserId);
            if (clientUser != null) {
                if (!clientUser.IsDeleted.Value) {
                    var deletedClientUser = await repo.DeleteUserById(clientUser);
                    return new SpfDeleteUserResponse(new ResponseStatus(request.userUserId, true, "Delete skipped: User has been deleted"));
                }
                else {
                    return new SpfDeleteUserResponse(new ResponseStatus(request.userUserId, false, "The user has already been deleted"));
                }
            }
            else {
                return new SpfDeleteUserResponse(new ResponseStatus(request.userUserId, false, "Delete skipped: User doesn't exist"));
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfDeleteUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfDeleteUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}