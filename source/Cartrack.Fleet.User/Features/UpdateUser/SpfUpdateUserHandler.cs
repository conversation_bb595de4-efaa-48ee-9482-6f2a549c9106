﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateUser;

public class SpfUpdateUserHandler(IUserRepository repo, ILogger<SpfUpdateUserHandler> logger)
    : IRequestHandler<SpfUpdateUserRequest, SpfUpdateUserResponse> {
    public async Task<SpfUpdateUserResponse> Handle(SpfUpdateUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.clientUserId, nameof(request.clientUserId));
            //Requires.NotNullOrEmpty(request.email, nameof(request.email));
            //Requires.NotNullOrEmpty(request.cellNumber, nameof(request.cellNumber));
            //Requires.NotNullOrEmpty(request.roleCode, nameof(request.roleCode));
            logger.LogInformation("Retrieving the user with clientUserId={clientUserId}", request.clientUserId);
            ResponseStatus updateStatus = null;
            var clientUser = await repo.GetUserById(request.clientUserId);
            if (clientUser != null) {
                var emailToBeUpdated = string.IsNullOrEmpty(request.email) ? clientUser.EMail : request.email;
                var cellToBeUpdated = string.IsNullOrEmpty(request.cellNumber) == null
                    ? clientUser.CellNumber
                    : request.cellNumber;
                var clientUserRole = await repo.GetUserRoleByCode(request.roleCode);
                var roleToBeUpdated = string.IsNullOrEmpty(clientUserRole.RoleUuid)
                    ? clientUser.ClientUserRoleId
                    : clientUserRole?.RoleUuid;

                var updatedClientUser =
                    await repo.UpdateUser(clientUser, emailToBeUpdated, cellToBeUpdated, roleToBeUpdated);
                updateStatus = new ResponseStatus(clientUser.ClientUserId, true, "User details have been updated");
                return new SpfUpdateUserResponse(updateStatus);
            }
            else {
                updateStatus = new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User account doesn't exist");
                return new SpfUpdateUserResponse(updateStatus);
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfUpdateUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfUpdateUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}