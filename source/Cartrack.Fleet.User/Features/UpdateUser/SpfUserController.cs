﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateUser;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updatebyid")]
    public async Task<ActionResult<SpfUpdateUserResponse>> UpdateUserById([FromBody] SpfUpdateUserRequest request) {
        var resp = await this._mediator.Send(new SpfUpdateUserRequest(request.clientUserId, request.email,
            request.cellNumber, request.roleCode));
        return resp.ToContentResult<SpfUpdateUserResponse, ResponseStatus>(this.HttpContext);
    }
}