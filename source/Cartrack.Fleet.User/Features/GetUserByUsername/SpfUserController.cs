﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUserByUsername;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("getbyusername")]
    public async Task<ActionResult<SpfGetUserByNameResponse>> GetUser(long userId, string userName) {
        var resp = await this._mediator.Send(new SpfGetUserByNameRequest(userId, userName));
        return resp.ToContentResult<SpfGetUserByNameResponse, ClientUser>(this.HttpContext);
    }
}