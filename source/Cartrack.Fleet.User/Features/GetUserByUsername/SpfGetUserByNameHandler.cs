﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUserByUsername;

public class SpfGetUserByNameHandler(IUserRepository repo, ILogger<SpfGetUserByNameHandler> logger)
    : IRequestHandler<SpfGetUserByNameRequest, SpfGetUserByNameResponse> {
    public async Task<SpfGetUserByNameResponse> Handle(SpfGetUserByNameRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.userId > 0, "User Id must be greater than zero");
            Requires.NotNullOrEmpty(request.userName, nameof(request.userName));
            logger.LogInformation("[{userId}] Retrieving the user with userName={userName}", request.userId,
                request.userName);

            var user = await repo.GetUserByUserName(request.userId, request.userName);
            if (user != null) {
                return new SpfGetUserByNameResponse(user);
            }
            else {
                return new SpfGetUserByNameResponse(null, new RequiresException("The client user doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetUserByNameResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetUserByNameResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}