﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.AddUserDepartment;

public class AddUserDepartmentHandler(IUserRepository repo, ILogger<AddUserDepartmentHandler> logger)
    : IRequestHandler<AddUserDepartmentRequest, AddUserDepartmentResponse> {
    public async Task<AddUserDepartmentResponse> Handle(AddUserDepartmentRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.DepartmentId > 0, "DepartmentId must be greater than zero");
            Requires.NotNullOrEmpty(request.clientUserId, nameof(request.clientUserId));
            Requires.NotNullOrEmpty(request.serviceType, nameof(request.serviceType));
            logger.LogInformation("Retrieving the user with clientUserId={clientUserId}", request.clientUserId);

            var validServiceTypes = new[] {
                "PERM", "NSF", "VSC", "NON", "EXT", "HTX"
            };
            if (!validServiceTypes.Contains(request.serviceType)) {
                return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    $"Create skipped: Service Type should be one of {string.Join(",", validServiceTypes)}"));
            }

            var clientUser = await repo.GetUserById(request.clientUserId);
            if (clientUser == null) {
                return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    "Create skipped: User account doesn't exist"));
            }

            var department = await repo.GetDepartmentInfo(request.DepartmentId, clientUser.UserId);
            if (department == null) {
                return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    $"Create skipped: {request.DepartmentId} is invalid departmentId"));
            }

            var clientUserDepartment =
                await repo.GetUserDepartmentByIdAndServiceType(request.clientUserId, request.serviceType);
            if (clientUserDepartment != null) {
                return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    "Create skipped: User department exists"));
            }

            //if (clientUserDepartment.DepartmentId == request.DepartmentId) {
            //    string statusMessage = $"Update skipped: Current department id is also {clientUserDepartment.DepartmentId}";
            //    return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, false, statusMessage));
            //}

            var clientUserDepartmentObj = new ClientUserDepartment() {
                ClientUserId = request.clientUserId,
                ServiceType = request.serviceType,
                DepartmentId = request.DepartmentId,
                IsActive = true
            };
            await repo.AddUserDepartment(clientUserDepartmentObj);

            string successMessage = "User department has been added";
            return new AddUserDepartmentResponse(new ResponseStatus(request.clientUserId, true, successMessage));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error updating user status");
            return new AddUserDepartmentResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error updating user status");
            return new AddUserDepartmentResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}