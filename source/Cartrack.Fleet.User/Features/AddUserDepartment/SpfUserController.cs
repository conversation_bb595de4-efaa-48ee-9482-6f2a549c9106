﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.AddUserDepartment;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPost]
    [Route("adduserdepartment")]
    public async Task<ActionResult<AddUserDepartmentResponse>> UpdateUserStatus(
        [FromBody] AddUserDepartmentRequest request) {
        var resp = await this._mediator.Send(new AddUserDepartmentRequest(request.clientUserId, request.serviceType,
            request.DepartmentId));
        return resp.ToContentResult<AddUserDepartmentResponse, ResponseStatus>(this.HttpContext);
    }
}