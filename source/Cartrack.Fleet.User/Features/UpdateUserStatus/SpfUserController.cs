﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateUserStatus;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updateuserstatus")]
    public async Task<ActionResult<UpdateUserStatusResponse>> UpdateUserStatus(
        [FromBody] UpdateUserStatusRequest request) {
        var resp = await this._mediator.Send(new UpdateUserStatusRequest(request.clientUserId, request.isEnabled));
        return resp.ToContentResult<UpdateUserStatusResponse, ResponseStatus>(this.HttpContext);
    }
}