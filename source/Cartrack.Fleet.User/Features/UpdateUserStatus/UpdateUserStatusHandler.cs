﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateUserStatus;

public class UpdateUserStatusHandler(IUserRepository repo, ILogger<UpdateUserStatusHandler> logger)
    : IRequestHandler<UpdateUserStatusRequest, UpdateUserStatusResponse> {
    public async Task<UpdateUserStatusResponse> Handle(UpdateUserStatusRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.clientUserId, nameof(request.clientUserId));

            logger.LogInformation("Retrieving the user with clientUserId={clientUserId}", request.clientUserId);

            int desiredStatusId = request.isEnabled ? UserDriverStatus.Enabled : UserDriverStatus.Disabled;

            var clientUser = await repo.GetUserById(request.clientUserId);
            if (clientUser == null) {
                return new UpdateUserStatusResponse(new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User account doesn't exist"));
            }

            var clientUserState = await repo.GetUserStatusById(request.clientUserId);
            if (clientUserState == null) {
                return new UpdateUserStatusResponse(new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User state doesn't exist"));
            }

            if (clientUserState.ClientUserStatusId == desiredStatusId) {
                string statusMessage = request.isEnabled
                    ? "Update skipped: Current status is already enabled"
                    : "Update skipped: Current status is already disabled";
                return new UpdateUserStatusResponse(new ResponseStatus(request.clientUserId, false, statusMessage));
            }

            if (request.isEnabled) {
                var clientDriver = await repo.GetDriverByClientUserId(request.clientUserId);
                if (clientDriver == null) {
                    return new UpdateUserStatusResponse(new ResponseStatus(request.clientUserId, false,
                        "Update skipped: Driver profile doesn't exist"));
                }
            }

            string note = request.isEnabled ? "User enabled" : "User disabled";
            var clientAction = await repo.AddClientAction(request.clientUserId, note, clientUser.UserId);
            await repo.UpdateUserStatus(clientUserState, clientAction.ClientActionId, desiredStatusId);

            string successMessage = request.isEnabled
                ? "User account has been enabled"
                : "User account has been disabled";
            return new UpdateUserStatusResponse(new ResponseStatus(request.clientUserId, true, successMessage));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error updating user status");
            return new UpdateUserStatusResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error updating user status");
            return new UpdateUserStatusResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}