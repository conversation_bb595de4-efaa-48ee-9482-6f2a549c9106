﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUserById;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("getbyid/{clientUserId}")]
    public async Task<ActionResult<SpfGetUserByResponse>> GetUserById(string clientUserId) {
        var resp = await this._mediator.Send(new SpfGetUserByRequest(clientUserId));
        return resp.ToContentResult<SpfGetUserByResponse, ClientUser>(this.HttpContext);
    }
}