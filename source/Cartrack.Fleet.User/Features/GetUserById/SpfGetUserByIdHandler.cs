﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUserById;

public class SpfGetUserByIdHandler(IUserRepository repo, ILogger<SpfGetUserByIdHandler> logger)
    : IRequestHandler<SpfGetUserByRequest, SpfGetUserByResponse> {
    public async Task<SpfGetUserByResponse> Handle(SpfGetUserByRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.userUserId, nameof(request.userUserId));
            logger.LogInformation("Retrieving the user with userUserId={userUserId}", request.userUserId);

            var user = await repo.GetUserById(request.userUserId);
            if (user != null) {
                return new SpfGetUserByResponse(user);
            }
            else {
                return new SpfGetUserByResponse(null, new Exception("The client user doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetUserByResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetUserByResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}