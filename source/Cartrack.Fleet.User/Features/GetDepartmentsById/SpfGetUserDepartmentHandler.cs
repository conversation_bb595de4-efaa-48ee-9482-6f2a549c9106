﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetDepartmentsById;

public class SpfGetUserDepartmentHandler(IUserRepository repo, ILogger<SpfGetUserDepartmentHandler> logger)
    : IRequestHandler<SpfGetUserDepartmentRequest, SpfGetUserDepartmentResponse> {
    public async Task<SpfGetUserDepartmentResponse> Handle(SpfGetUserDepartmentRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.userUserId, nameof(request.userUserId));
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving the user with userUserId={userUserId}", request.Account,
                request.userUserId);

            var userDepartments = await repo.GetUserDepartmentById(request.userUserId);
            return new SpfGetUserDepartmentResponse(userDepartments);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetUserDepartmentResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetUserDepartmentResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}