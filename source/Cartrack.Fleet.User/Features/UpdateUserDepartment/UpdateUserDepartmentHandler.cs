﻿using Cartrack.AppHost;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.UpdateUserDepartment;

public class UpdateUserDepartmentHandler(IUserRepository repo, ILogger<UpdateUserDepartmentHandler> logger)
    : IRequestHandler<UpdateUserDepartmentRequest, UpdateUserDepartmentResponse> {
    public async Task<UpdateUserDepartmentResponse> Handle(UpdateUserDepartmentRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.DepartmentId > 0, "DepartmentId must be greater than zero");
            Requires.NotNullOrEmpty(request.clientUserId, nameof(request.clientUserId));
            Requires.NotNullOrEmpty(request.serviceType, nameof(request.serviceType));
            logger.LogInformation("Retrieving the user with clientUserId={clientUserId}", request.clientUserId);

            var validServiceTypes = new[] {
                "PERM", "NSF", "VSC", "NON", "EXT", "HTX"
            };
            if (!validServiceTypes.Contains(request.serviceType)) {
                return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    $"Update skipped: Service Type should be one of {string.Join(",", validServiceTypes)}"));
            }

            var clientUser = await repo.GetUserById(request.clientUserId);
            if (clientUser == null) {
                return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User account doesn't exist"));
            }

            var department = await repo.GetDepartmentInfo(request.DepartmentId, clientUser.UserId);
            if (department == null) {
                return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    $"Update skipped: {request.DepartmentId} is invalid departmentId"));
            }

            var clientUserDepartment =
                await repo.GetUserDepartmentByIdAndServiceType(request.clientUserId, request.serviceType);
            if (clientUserDepartment == null) {
                return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, false,
                    "Update skipped: User department doesn't exist"));
            }

            if (clientUserDepartment.DepartmentId == request.DepartmentId) {
                string statusMessage =
                    $"Update skipped: Current department id is also {clientUserDepartment.DepartmentId}";
                return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, false, statusMessage));
            }

            await repo.UpdateUserDepartment(clientUserDepartment, request.DepartmentId);

            string successMessage = "User department has been updated";
            return new UpdateUserDepartmentResponse(new ResponseStatus(request.clientUserId, true, successMessage));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error updating user status");
            return new UpdateUserDepartmentResponse(new ResponseStatus(null, null, null), ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error updating user status");
            return new UpdateUserDepartmentResponse(new ResponseStatus(null, null, null), ex) {
                IsServerError = true
            };
        }
    }
}