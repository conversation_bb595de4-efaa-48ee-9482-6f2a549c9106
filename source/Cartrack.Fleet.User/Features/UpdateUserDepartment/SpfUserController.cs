﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.Infratructure;
using Cartrack.Fleet.User.Features.UpdateUserDepartment;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpPatch]
    [Route("updateuserdepartment")]
    public async Task<ActionResult<UpdateUserDepartmentResponse>> UpdateUserStatus(
        [FromBody] UpdateUserDepartmentRequest request) {
        var resp = await this._mediator.Send(new UpdateUserDepartmentRequest(request.clientUserId, request.serviceType,
            request.DepartmentId));
        return resp.ToContentResult<UpdateUserDepartmentResponse, ResponseStatus>(this.HttpContext);
    }
}