﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.User.Features.GetUsers;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.User.IO.Http;

public partial class UserController {
    [HttpGet]
    [Route("{userId}")]
    public async Task<ActionResult<SpfGetUserResponse>> GetUsers(long userId) {
        var resp = await this._mediator.Send(new SpfGetUserRequest(userId));
        return resp.ToContentResult<SpfGetUserResponse, List<ClientUser>>(this.HttpContext);
    }
}