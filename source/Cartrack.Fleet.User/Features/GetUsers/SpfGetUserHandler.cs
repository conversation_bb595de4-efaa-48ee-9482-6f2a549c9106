﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.User.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.User.Features.GetUsers;

public class SpfGetUserHandler(IUserRepository repo, ILogger<SpfGetUserHandler> logger)
    : IRequestHandler<SpfGetUserRequest, SpfGetUserResponse> {
    public async Task<SpfGetUserResponse> Handle(SpfGetUserRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.userId > 0, "User Id must be greater than zero");
            logger.LogInformation("[{userId}] Retrieving the users", request.userId);

            var users = await repo.GetUsers(request.userId);
            if (users.Any()) {
                return new SpfGetUserResponse(users);
            }
            else {
                return new SpfGetUserResponse(null, new Exception("Client users doesn't exist")) {
                    IsServerError = false
                };
            }
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetUserResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetUserResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}