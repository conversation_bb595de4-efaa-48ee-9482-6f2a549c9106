﻿using Cartrack.AppHost;
using Cartrack.Fleet.Accessory.IO;
using Cartrack.Fleet.Accessory.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf;

public class ScdfGetAccessoryHandler(IAccessoryRepository repo, ILogger<ScdfGetAccessoryHandler> logger)
    : IRequestHandler<ScdfGetAccessoryRequest, ScdfGetAccessoryResponse> {
    public async Task<ScdfGetAccessoryResponse> Handle(ScdfGetAccessoryRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving the accessory with Id={AccessoryId}", request.Account,
                request.Id);

            var accessory = await repo.GetAccessoryById(request.UserId, request.Id);
            var apiBooking = accessory?.ToHttpAccessory();
            return new ScdfGetAccessoryResponse(apiBooking);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Accessory");
            return new ScdfGetAccessoryResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Accessory");
            return new ScdfGetAccessoryResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}