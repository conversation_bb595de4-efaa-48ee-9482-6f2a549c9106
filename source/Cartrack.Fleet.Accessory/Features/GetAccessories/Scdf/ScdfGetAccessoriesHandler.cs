﻿using Cartrack.AppHost;
using Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf;
using Cartrack.Fleet.Accessory.IO;
using Cartrack.Fleet.Accessory.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http.Timeouts;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf;

public class ScdfGetAccessoriesHandler(IAccessoryRepository repo, ILogger<ScdfGetAccessoriesHandler> logger)
    : IRequestHandler<ScdfGetAccessoriesRequest, ScdfGetAccessoriesResponse> {
    public async Task<ScdfGetAccessoriesResponse> Handle(ScdfGetAccessoriesRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving all accessories", request.Account);

            var accessories = await repo.GetAccessories(request.UserId);
            var total = await repo.GetTotal(request.UserId);
            var apiBooking = accessories?.ToHttpAccessories(total);
            return new ScdfGetAccessoriesResponse(apiBooking);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Accessory");
            return new ScdfGetAccessoriesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Accessory");
            return new ScdfGetAccessoriesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}