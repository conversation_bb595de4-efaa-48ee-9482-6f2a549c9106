﻿using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;

namespace Cartrack.Fleet.Driver.IO.Http;

public class Driver {
    //TODO add the properties required by the client UI to display a driver
    public required string ClientDriverId { get; set; }
    public string? FirstName { get; set; }
    public string Surname { get; set; }
    public string IdNumber { get; set; }
    public Gender Gender { get; set; }
    public string PhoneNumber { get; set; }
    public DateTime? CreatedTs { get; set; }
    public DateTimeOffset? UpdatedTs { get; set; }
    public string ClientUserId { get; set; }
    public string Email { get; set; }
    public string? Enabled { get; set; }

    public string? EmployeeNumber { get; set; }

    //public bool IsDeleted { get; set; }
    public List<DriverDepartment> Departments { get; set; }
    public List<DriverQdlLicense> DriverLicenses { get; set; }
    public List<DriverPdpLicense> SpecialDriverLicenses { get; set; }
}

public record DriverStatus(long ClientDriverId, string Status);