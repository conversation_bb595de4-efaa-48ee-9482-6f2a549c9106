﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Driver.Domain;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository(AppFleetDbContext fleetDbContext, AppSettings appSettings) : IDriverRepository {
    public async Task<List<DriverPdpLicense>> GetPdpLicenses(int userId) {
        var pdpLicenses = await fleetDbContext.DriverSpecialLicenseTypes
            .Where(c => c.UserId == userId || c.UserId == null)
            .ToListAsync();

        List<DriverPdpLicense> driverPdpLicenses = [];
        driverPdpLicenses.AddRange(pdpLicenses
            .Select(p => new DriverPdpLicense() {
                LicenseTypeId = p.DriverSpecialLicenseTypeId, LicenseName = p.LicenseName
            })
        );

        return driverPdpLicenses;
    }

    public async Task<List<DriverQdlLicense>> GetQdlLicenses(int userId) {
        var qdlLicenses = await fleetDbContext.DriverLicenseTypes
            .Where(c => c.UserId == userId || c.UserId == null)
            .ToListAsync();

        List<DriverQdlLicense> driverQdlLicenses = [];
        driverQdlLicenses.AddRange(qdlLicenses
            .Select(p => new DriverQdlLicense() {
                LicenseTypeId = p.DriverLicenseTypeId, LicenseName = p.Name
            })
        );

        return driverQdlLicenses;
    }
}