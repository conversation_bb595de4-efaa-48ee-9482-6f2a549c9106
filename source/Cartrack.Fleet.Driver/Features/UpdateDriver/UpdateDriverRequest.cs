﻿using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Driver.Features.UpdateDriver;

public record UpdateDriverRequest : IRequest<UpdateDriverResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF000001";
    [JsonIgnore] public int UserId { get; init; } = 302739;

    [JsonIgnore] public string ClientDriverId { get; set; } = "";
    public string? DriverName { get; set; }
    public string? DriverSurName { get; set; }
    public string? PhoneNumber { get; set; }
    public string? EMail { get; set; }
    public string? IdNumber { get; set; }
    public int? Gender { get; init; } = 0;
    public int? Enabled { get; set; }
    public List<long>? DepartmentIds { get; set; }
    public List<DriverQdlLicense>? DriverLicenses { get; set; }
    public List<DriverPdpLicense>? SpecialDriverLicenses { get; set; }
    public string? EmployeeNumber { get; set; }

    /* Not needed for now
    public string? SocialSecurityNumber { get; set; }
    public DateTime? HiredDate { get; set; }
    public DateTime? LeaveDate { get; set; }
    public string? WorkStartTime { get; set; }
    public string? WorkEndTime { get; set; }
    public string? LaborRate { get; set; }
    public string? BillingRate { get; set; }
    public string? MonthlyWageRate { get; set; }
    public string? ManagerName { get; set; }*/
    // if there is anymore, please feel free to insert!
}