﻿using Cartrack.Fleet.Driver.Features.UpdateDriver;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpPatch]
    [Route("{clientDriverId}")]
    public async Task<ActionResult<UpdateDriverResponse>> UpdateDriver(string clientDriverId,
        [FromBody] UpdateDriverRequest request) {
        request.ClientDriverId = clientDriverId;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<UpdateDriverResponse, Driver>(this.HttpContext);
    }
}