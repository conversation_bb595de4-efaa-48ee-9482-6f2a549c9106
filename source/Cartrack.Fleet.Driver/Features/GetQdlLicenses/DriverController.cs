﻿using Cartrack.Fleet.Driver.Features.GetQdlLicenses;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("qdllicenses/{clientDriverId}")]
    public async Task<ActionResult<GetQdlLicensesResponse>> GetQdlLicenses(string clientDriverId) {
        var resp = await this._mediator.Send(new GetQdlLicensesRequest(clientDriverId));
        return resp.ToContentResult<GetQdlLicensesResponse, DriverQdlLicenses>(this.HttpContext);
    }
}