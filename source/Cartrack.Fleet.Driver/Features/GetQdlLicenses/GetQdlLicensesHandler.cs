﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetQdlLicenses;

public class GetQdlLicensesHandler(IDriverRepository repo, ILogger<GetQdlLicensesHandler> logger)
    : IRequestHandler<GetQdlLicensesRequest, GetQdlLicensesResponse> {
    public async Task<GetQdlLicensesResponse>
        Handle(GetQdlLicensesRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving the Driver QDL License with DriverId={DriverId}",
                request.Account, request.Id);

            var accessory = await repo.GetQdlLicensesById(request.Id);
            var apiBooking = accessory?.ToHttpQdlLicenses();
            return new GetQdlLicensesResponse(apiBooking);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving QDL Licenses");
            return new GetQdlLicensesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving QDL Licenses");
            return new GetQdlLicensesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}