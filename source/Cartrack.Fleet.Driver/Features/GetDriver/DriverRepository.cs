﻿using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Algorithm;
using static Cartrack.Fleet.Driver.Domain.Status;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
    public async Task<DriverBase?> GetDriverById(long userId, string clientDriverId) {
        var departments =
            await fleetDbContext.Departments
                .Where(y => y.UserId == userId && !y.IsDeleted)
                .Select(x => new {
                    x.Department<PERSON>d,
                    x.<PERSON>,
                    x.Description
                })
                .ToListAsync();

        var qdlLicenses = await fleetDbContext.DriverLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null)
            .Select(x => new {
                x.DriverLicenseTypeId, x.Name
            })
            .ToListAsync();

        var pdpLicenses = await fleetDbContext.DriverSpecialLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null)
            .Select(x => new {
                x.DriverSpecialLicenseTypeId, x.LicenseName
            })
            .ToListAsync();

        var drivers = await fleetDbContext.ClientDrivers
            .Where(x => x.UserId == userId && x.ClientDriverId.Equals(clientDriverId))
            .ToListAsync();

        var clientDriver = (from driver in drivers
                join cdd in fleetDbContext.ClientDriverDepartments on driver.ClientDriverId equals cdd.ClientDriverId
                    into cddJoin
                from cdd in cddJoin.Where(x => x.IsActive == true).DefaultIfEmpty()
                join qdl in fleetDbContext.ClientDriverLicenses on driver.ClientDriverId equals qdl.ClientDriverId into
                    qdlJoin
                from qdl in qdlJoin.DefaultIfEmpty()
                join pdp in fleetDbContext.ClientDriverSpecialLicenses on driver.ClientDriverId equals pdp
                    .ClientDriverId into pdpJoin
                from pdp in pdpJoin.DefaultIfEmpty()
                join cds in fleetDbContext.ClientDriverStates on driver.ClientDriverId equals cds.ClientDriverId into
                    cdsJoin
                from cds in cdsJoin
                    .Where(s => s.ClientDriverStatusId == (int)Active || s.ClientDriverStatusId == (int)Disabled)
                    .DefaultIfEmpty()
                group new {
                    driver,
                    cdd,
                    pdp,
                    qdl,
                    cds
                } by driver
                into g
                select new {
                    g.Key.ClientDriverId,
                    g.Key.UserId,
                    g.Key.DriverName,
                    g.Key.DriverSurname,
                    g.Key.IdNumber,
                    g.Key.Gender,
                    g.Key.CellNumber,
                    g.Key.Cts,
                    g.Key.Uts,
                    g.Key.EMail,
                    g.Key.EmployeeNumber,
                    g.Key.IsEncrypted,
                    Enabled = g.First().cds.ClientDriverStatusId,
                    Departments =
                        g.Where(x => x.cdd != null).GroupBy(x => x.cdd.DepartmentId).Select(x =>
                            new DriverDepartment {
                                Id = x.Key ?? 0, Name = departments.FirstOrDefault(a => a.DepartmentId == x.Key).DepartmentName,
                            }).ToList(),
                    DriverLicenses =
                        g.Where(x => x.qdl != null).GroupBy(x => x.qdl.DriverLicenseTypeId).Select(x =>
                            new DriverQdlLicense {
                                LicenseTypeId = x.Key,
                                LicenseName =
                                    qdlLicenses.Where(a => a.DriverLicenseTypeId == x.Key).Select(a => a.Name)
                                        .FirstOrDefault(),
                                LicenseNumber = x.First().qdl.LicenseNumber,
                                LicenseValidStart = x.First().qdl.LicenseValidStart,
                                LicenseValidEnd = x.First().qdl.LicenseValidEnd,
                                LicenseFirstIssueDate = x.First().qdl.LicenseFirstIssueDate,
                                LicensePoints = x.First().qdl.LicensePoints,
                                LicenseIssuedCountry = x.First().qdl.LicenseIssuedCountry,
                                LicenseDriverRestrictions = x.First().qdl.LicenseDriverRestrictions
                            }).ToList(),
                    SpecialDriverLicenses = g.Where(x => x.pdp != null).GroupBy(x => x.pdp.DriverSpecialLicenseTypeId)
                        .Select(x => new DriverPdpLicense {
                            LicenseTypeId = x.Key,
                            LicenseName =
                                pdpLicenses.Where(a => a.DriverSpecialLicenseTypeId == x.Key).Select(a => a.LicenseName)
                                    .FirstOrDefault(),
                            LicenseNumber = x.First().pdp.SpecialLicenseNumber,
                            LicenseIssueDate = x.First().pdp.SpecialLicenseIssueDate,
                            LicenseExpiryDate = x.First().pdp.SpecialLicenseExpiryDate
                        }).ToList()
                }
            ).FirstOrDefault();

        if (clientDriver is null) {
            return null;
        }

        var driverRepo = new DriverBase {
            ClientDriverId = clientDriver.ClientDriverId,
            UserId = clientDriver.UserId,
            FirstName = clientDriver.DriverName,
            Surname = clientDriver.DriverSurname ?? "",
            IdNumber = !string.IsNullOrEmpty(clientDriver.IdNumber) && clientDriver.IsEncrypted == true
                ? AesEncryption.Decrypt(clientDriver.IdNumber, appSettings.TfmsEncryptionKey,
                    appSettings.TfmsEncryptionIv)
                : clientDriver.IdNumber ?? "",
            Gender = clientDriver.Gender switch {
                1 => Gender.Male,
                2 => Gender.Female,
                _ => Gender.Unknown
            },
            PhoneNumber = clientDriver.CellNumber ?? "",
            CreatedTs = clientDriver.Cts?.ToLocalTime(),
            UpdatedTs = clientDriver.Uts?.ToLocalTime(),
            Email = clientDriver.EMail ?? "",
            Enabled = clientDriver.Enabled switch {
                10 => Status.Active,
                11 => Status.Disabled,
                _ => Status.Unknown
            },
            Departments = clientDriver.Departments,
            DriverLicenses = clientDriver.DriverLicenses,
            SpecialDriverLicenses = clientDriver.SpecialDriverLicenses,
            EmployeeNumber = !string.IsNullOrEmpty(clientDriver.EmployeeNumber) && clientDriver.IsEncrypted == true
                ? AesEncryption.Decrypt(clientDriver.EmployeeNumber, appSettings.TfmsEncryptionKey,
                    appSettings.TfmsEncryptionIv)
                : clientDriver.EmployeeNumber ?? ""
        };

        return driverRepo;
    }
}