﻿using Cartrack.Fleet.Driver.Features.GetDriver;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("{clientDriverId}")]
    public async Task<ActionResult<GetDriverResponse>> GetDriver(string clientDriverId) {
        var resp = await this._mediator.Send(new GetDriverRequest(clientDriverId));
        return resp.ToContentResult<GetDriverResponse, Driver>(this.HttpContext);
    }
}