﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Common.Helper;
using Cartrack.Fleet.Driver.Domain;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Driver.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class DriverRepository {
    private const int ActiveDriver = 10;
    private const int InactiveDriver = 11;

    public async Task<List<DriverBase>> GetDrivers(long userId, int page, int pageSize) {
        var qdlLicenses = await fleetDbContext.DriverLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null).ToListAsync();
        var pdpLicenses = await fleetDbContext.DriverSpecialLicenseTypes
            .Where(x => x.UserId == userId || x.UserId == null).ToListAsync();

        var qdlLicensesDict = qdlLicenses.ToDictionary(x => x.DriverLicenseTypeId, x => x.Name);
        var pdpLicensesDict = pdpLicenses.ToDictionary(x => x.DriverSpecialLicenseTypeId, x => x.LicenseName);

        var drivers = await (from driver in fleetDbContext.ClientDrivers
                where driver.UserId == userId
                //&& driver.ClientDriverStates.Any(s => s.ClientDriverStatusId == ActiveDriver)
                join cds in fleetDbContext.ClientDriverStates on driver.ClientDriverId equals cds.ClientDriverId into
                    cdsJoin
                from cds in cdsJoin.DefaultIfEmpty()
                join cdd in fleetDbContext.ClientDriverDepartments on driver.ClientDriverId equals cdd.ClientDriverId
                    into cddJoin
                from cdd in cddJoin.DefaultIfEmpty()
                join dept in fleetDbContext.Departments on cdd.DepartmentId equals dept.DepartmentId into deptJoin
                from dept in deptJoin.Where(x => !x.IsDeleted).DefaultIfEmpty()
                join qdl in fleetDbContext.ClientDriverLicenses on driver.ClientDriverId equals qdl.ClientDriverId into
                    qdlJoin
                from qdl in qdlJoin.DefaultIfEmpty()
                join pdp in fleetDbContext.ClientDriverSpecialLicenses on driver.ClientDriverId equals pdp
                    .ClientDriverId into pdpJoin
                from pdp in pdpJoin.DefaultIfEmpty()
                group new {
                    driver,
                    cdd,
                    cds,
                    dept,
                    pdp,
                    qdl
                } by driver
                into g
                select new {
                    g.Key.ClientDriverId,
                    g.Key.UserId,
                    g.Key.DriverName,
                    g.Key.DriverSurname,
                    g.Key.IdNumber,
                    g.Key.Gender,
                    g.Key.CellNumber,
                    g.Key.Cts,
                    g.Key.Uts,
                    g.Key.EMail,
                    g.Key.EmployeeNumber,
                    g.Key.IsEncrypted,
                    Enabled = g.GroupBy(a => a.cds.ClientDriverStatusId).Select(x => x.Key).FirstOrDefault(),
                    Departments =
                        g.GroupBy(x => x.dept.DepartmentId).Select(x =>
                            new DriverDepartment {
                                Id = x.Key, Name = x.First().dept.DepartmentName
                            }).ToList(),
                    DriverLicenses = g.GroupBy(x => x.qdl.DriverLicenseTypeId).Select(x => new DriverQdlLicense {
                        LicenseTypeId = x.Key,
                        LicenseName = qdlLicensesDict.GetValueOrDefault(x.Key),
                        LicenseNumber = x.First().qdl.LicenseNumber,
                        LicenseValidStart = x.First().qdl.LicenseValidStart,
                        LicenseValidEnd = x.First().qdl.LicenseValidEnd,
                        LicenseFirstIssueDate = x.First().qdl.LicenseFirstIssueDate,
                        LicensePoints = x.First().qdl.LicensePoints,
                        LicenseIssuedCountry = x.First().qdl.LicenseIssuedCountry,
                        LicenseDriverRestrictions = x.First().qdl.LicenseDriverRestrictions
                    }).ToList(),
                    SpecialDriverLicenses = g.GroupBy(x => x.pdp.DriverSpecialLicenseTypeId).Select(x =>
                        new DriverPdpLicense {
                            LicenseTypeId = x.Key,
                            LicenseName = pdpLicensesDict.GetValueOrDefault(x.Key),
                            LicenseNumber = x.First().pdp.SpecialLicenseNumber,
                            LicenseIssueDate = x.First().pdp.SpecialLicenseIssueDate,
                            LicenseExpiryDate = x.First().pdp.SpecialLicenseExpiryDate
                        }).ToList()
                }
            )
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        List<DriverBase> driversList = [];
        driversList.AddRange(drivers
            .Select(driver => new DriverBase() {
                ClientDriverId = driver.ClientDriverId,
                UserId = driver.UserId,
                FirstName = driver.DriverName,
                Surname = driver.DriverSurname ?? "",
                IdNumber = !string.IsNullOrEmpty(driver.IdNumber) && driver.IsEncrypted == true
                    ? AesEncryption.Decrypt(driver.IdNumber, appSettings.TfmsEncryptionKey,
                        appSettings.TfmsEncryptionIv)
                    : driver.IdNumber ?? "",
                Gender = driver.Gender switch {
                    1 => Gender.Male,
                    2 => Gender.Female,
                    _ => Gender.Unknown
                },
                Enabled = driver.Enabled switch {
                    10 => Status.Active,
                    11 => Status.Disabled,
                    _ => Status.Unknown
                },
                PhoneNumber = driver.CellNumber ?? "",
                CreatedTs = driver.Cts,
                UpdatedTs = driver.Uts,
                Email = driver.EMail ?? "",
                EmployeeNumber = !string.IsNullOrEmpty(driver.EmployeeNumber) && driver.IsEncrypted == true
                    ? AesEncryption.Decrypt(driver.EmployeeNumber, appSettings.TfmsEncryptionKey,
                        appSettings.TfmsEncryptionIv)
                    : driver.EmployeeNumber ?? "",
                Departments = driver.Departments,
                DriverLicenses = driver.DriverLicenses,
                SpecialDriverLicenses = driver.SpecialDriverLicenses
            })
        );

        return driversList;
    }

    public async Task<int> GetTotal(int userId) {
        return await fleetDbContext.ClientDrivers
            .Where(c => c.UserId == userId && c.ClientDriverStates.Any(s => s.ClientDriverStatusId == ActiveDriver))
            .CountAsync();
    }
}