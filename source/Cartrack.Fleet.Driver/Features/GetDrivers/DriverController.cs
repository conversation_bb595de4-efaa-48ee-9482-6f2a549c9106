﻿using Cartrack.Fleet.Driver.Features.GetDrivers;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    public async Task<ActionResult<GetDriversResponse>> GetDrivers([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetDriversRequest(page, pageSize));
        return resp.ToContentResult<GetDriversResponse, Drivers>(this.HttpContext);
    }
}