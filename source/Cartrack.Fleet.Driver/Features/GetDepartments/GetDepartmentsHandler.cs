﻿using Cartrack.AppHost;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Driver.Features.GetDepartments;

public class GetDepartmentsHandler(IDriverRepository repo, ILogger<GetDepartmentsHandler> logger)
    : IRequestHandler<GetDepartmentsRequest, GetDepartmentsResponse> {
    public async Task<GetDepartmentsResponse>
        Handle(GetDepartmentsRequest request, CancellationToken cancellationToken) {
        try {
            //Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving all driver department belong to {ClientDriverId}",
                request.Account, request.ClientDriverId);

            var driverDepartments = await repo.GetDepartmentsByDriverId(request.UserId, request.ClientDriverId);
            var apiDriver = driverDepartments?.ToHttpDriverDepartments();
            return new GetDepartmentsResponse(apiDriver);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Client Driver Departments");
            return new GetDepartmentsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Client Driver Department");
            return new GetDepartmentsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}