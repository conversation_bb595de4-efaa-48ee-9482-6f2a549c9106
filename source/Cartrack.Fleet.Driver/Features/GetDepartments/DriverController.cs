﻿using Cartrack.Fleet.Driver.Features.GetDepartments;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("departments/{clientDriverId}")]
    public async Task<ActionResult<GetDepartmentsResponse>> GetDepartments(string clientDriverId) {
        var resp = await this._mediator.Send(new GetDepartmentsRequest(clientDriverId));
        return resp.ToContentResult<GetDepartmentsResponse, DriverDepartments>(this.HttpContext);
    }
}