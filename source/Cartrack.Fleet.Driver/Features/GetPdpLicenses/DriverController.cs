﻿using Cartrack.Fleet.Driver.Features.GetPdpLicenses;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.Domain;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Driver.IO.Http;

public partial class DriverController {
    [HttpGet]
    [Route("pdplicenses/{clientDriverId}")]
    public async Task<ActionResult<GetPdpLicensesResponse>> GetPdpLicenses(string clientDriverId) {
        var resp = await this._mediator.Send(new GetPdpLicensesRequest(clientDriverId));
        return resp.ToContentResult<GetPdpLicensesResponse, DriverPdpLicenses>(this.HttpContext);
    }
}