﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Driver.IO;
using Cartrack.Fleet.Driver.IO.Sql;
using System.Net.Mail;

namespace Cartrack.Fleet.Driver.Domain.Common;

public class DriverBase : Driver {
    public override Task Validate() {
        // Check Gender Value
        if (!Enum.IsDefined(typeof(Gender), this.Gender)) {
            throw new ArgumentException("Invalid 'gender' specified. e.g: 1 = Male, 2 = Female, 0 = Unknown ");
        }

        // Check Status Driver Enabled Value
        var enabled = this.Enabled;
        if (enabled != null && (!Enum.IsDefined(typeof(Status), enabled) || this.Enabled == Status.Unknown)) {
            throw new ArgumentException("Invalid 'enabled' specified. e.g: 1 = Active, 0 = Disabled ");
        }

        // Check Email Format is Correct
        var email = this.Email ?? null;
        if (email != null) {
            try {
                var _ = new MailAddress(email);
            }
            catch (FormatException) {
                throw new ArgumentException("Invalid email format. e.g: <EMAIL>");
            }
        }

        // Check Valid Departments
        if (this.Departments != null && this.DepartmentsMasterList != null) {
            var invalidIds = this.Departments
                .Select(x => x.Id)
                .Except(this.DepartmentsMasterList.Select(x => x.DepartmentId)).ToList();

            if (invalidIds.Count > 0) {
                throw new ArgumentException("Invalid department ids = " + string.Join(",", invalidIds));
            }
        }

        // Check QDL License has valid Id
        if (this.DriverLicenses != null && this.QdlMasterList != null) {
            var invalidIds = this.DriverLicenses
                .Select(x => x.LicenseTypeId)
                .Except(this.QdlMasterList.Select(x => x.LicenseTypeId)).ToList();

            if (invalidIds.Count > 0) {
                throw new ArgumentException("Invalid driver license type ids = " + string.Join(",", invalidIds));
            }
        }

        // Check PDP License has valid Id
        if (this.SpecialDriverLicenses != null && this.PdpMasterList != null) {
            var invalidIds = this.SpecialDriverLicenses
                .Select(x => x.LicenseTypeId)
                .Except(this.PdpMasterList.Select(x => x.LicenseTypeId)).ToList();

            if (invalidIds.Count > 0) {
                throw new ArgumentException("Invalid driver special license type ids = " +
                                            string.Join(",", invalidIds));
            }
        }

        return Task.CompletedTask;
    }
}