﻿using Cartrack.EFCore.Models.Fleet;
using MainCommon = Cartrack.Fleet.Common;
using Cartrack.Fleet.Driver.IO.Sql;

namespace Cartrack.Fleet.Driver.Domain.Common;

public abstract class Driver : IDriver {
    public string ClientDriverId { get; set; }
    public long UserId { get; set; }
    public string FirstName { get; set; }
    public string Surname { get; set; } = "";
    public string FullName => $"{Surname} {FirstName}";
    public string IdNumber { get; set; }
    public Gender Gender { get; set; }
    public string PhoneNumber { get; set; }
    public DateTime? CreatedTs { get; set; }
    public DateTime? UpdatedTs { get; set; }
    public string? ClientUserId { get; set; }
    public string Email { get; set; }
    public Status? Enabled { get; set; }
    public List<DriverDepartment>? Departments { get; set; }
    public List<DriverQdlLicense>? DriverLicenses { get; set; }
    public List<DriverPdpLicense>? SpecialDriverLicenses { get; set; }
    public string EmployeeNumber { get; set; }
    public List<Department>? DepartmentsMasterList { get; set; }
    public List<DriverQdlLicense>? QdlMasterList { get; set; }
    public List<DriverPdpLicense>? PdpMasterList { get; set; }
    public abstract Task Validate();
}