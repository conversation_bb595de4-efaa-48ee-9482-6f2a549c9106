﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.IO.FileSystem;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Auth.IO.Http;

[ApiController]
[Route("auth")]
public partial class AuthController(
    AppSettings settings,
    IJwtService jwtService,
    AppFleetDbContext fleetDbContext,
    AppCtDbContext ctDbContext,
    IMediator mediator,
    IFileIo fileIo,
    IHttpContextAccessor httpContext)
    : ControllerBase {
}