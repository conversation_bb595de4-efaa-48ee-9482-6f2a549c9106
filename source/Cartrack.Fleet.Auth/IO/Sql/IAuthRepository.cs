﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;

namespace Cartrack.Fleet.Auth.IO.Sql;

public interface IAuthRepository {
    Task<ClientUser?> GetClientUser(string username, CancellationToken token);
    Task AddRefreshToken(ClientUser user, string refreshToken);

    Task<ClientUserToken?> GetRefreshToken(string refreshToken);

    Task UpdateRefreshToken(ClientUserToken refreshToken, string newRefreshToken);
    Task RevokeToken(ClientUserToken refreshToken);

    Task<User?> GetUser(long userId);

    Task<User?> GetUserByUserName(string username);
}