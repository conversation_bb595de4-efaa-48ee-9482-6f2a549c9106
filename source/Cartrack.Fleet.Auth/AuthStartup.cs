﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.Features.Login;
using Cartrack.Fleet.Auth.IO.FileSystem;
using Cartrack.Fleet.Auth.IO.Http;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using AuthRepository = Cartrack.Fleet.Auth.IO.Sql.AuthRepository;

namespace Cartrack.Fleet.Auth;

public static class AuthStartup {
    public static void Register(IServiceCollection builderServices, AppSettings settings) {
        builderServices.AddScoped<IAuthRepository, AuthRepository>();
        builderServices.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<AuthController>());
        builderServices.AddControllers().AddApplicationPart(typeof(AuthController).Assembly);

        builderServices.AddScoped<IJwtService, JwtService>();
        builderServices.AddScoped<IRsaKeyService, RsaKeyService>();
        builderServices.AddScoped<IPasswordHasher<ClientUser>, PasswordHasher<ClientUser>>();
        builderServices.AddScoped<IFileIo, FileIo>();

        // Configure JWT authentication
        var rsaKeyService = new RsaKeyService(settings, new FileIo());
        var publicKey = rsaKeyService.GetPublicKey();

        builderServices.AddAuthentication(x => {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x => {
                x.RequireHttpsMetadata = true;
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new RsaSecurityKey(publicKey),
                    ValidateIssuer = true,
                    ValidIssuer = settings.Issuer,
                    ValidateAudience = true,
                    ValidAudience = settings.Audience,
                };
            });
    }
}