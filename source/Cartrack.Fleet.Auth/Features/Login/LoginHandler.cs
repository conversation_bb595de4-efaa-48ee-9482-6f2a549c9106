﻿using Cartrack.EFCore.Models.CT;
using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;

namespace Cartrack.Fleet.Auth.Features.Login;

public class <PERSON>ginHandler(AppSettings settings, IAuthRepository repository, IJwtService jwtService)
    : IRequestHandler<LoginRequest, LoginResponse> {
    public async Task<LoginResponse> Handle(LoginRequest request, CancellationToken token) {
        bool isMainUser = false;
        ClientUser clientUser = null;
        var mainUsers = settings.MainUser.Split(',').Select(u => u.Trim());
        if (mainUsers.Any(u => u.Equals(request.Username, StringComparison.OrdinalIgnoreCase))) {
            var user = await repository.GetUserByUserName(request.Username);
            if (user is null) {
                return new LoginResponse(null);
            }
            else {
                isMainUser = true;
                clientUser = new ClientUser() { 
                    UserName = user.UserName, 
                    UserId = user.UserId, 
                    ClientUserId = user.UserId.ToString(), 
                    PasswordHash = user.PasswordHash 
                };
            }
        }
        else {
            clientUser = await repository.GetClientUser(request.Username, token);
            if (clientUser is null) {
                return new LoginResponse(null);
            }
        }

        PasswordVerificationResult result;
        if (isMainUser || settings.IsValidatingPassword) {
            result = IsPasswordValid(request.Password, clientUser.PasswordHash);
            if (result == PasswordVerificationResult.Failed) {
                return new LoginResponse(Domain.Login.Empty()) {
                    PasswordVerificationResult = result,
                    IsServerError = false
                };
            }
        }
        else {
            result = PasswordVerificationResult.Success;
        }

        var accessToken = jwtService.GenerateAccessToken(clientUser, request.Account);
        var refreshToken = jwtService.GenerateRefreshToken(clientUser, request.Account);
        if (!isMainUser) {
            await repository.AddRefreshToken(clientUser, refreshToken);
        }

        return new LoginResponse(new Domain.Login {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresAtUtc = DateTime.UtcNow.AddMinutes(settings.TokenValidityMinutes)
        }) {
            PasswordVerificationResult = result
        };
    }

    private PasswordVerificationResult IsPasswordValid(string password, string expectedPasswordHash) {
        string GeneratePasswordHash(string pw) {
            using (SHA1 sha1 = SHA1.Create()) {
                byte[] inputBytes = Encoding.UTF8.GetBytes(password);
                byte[] hashBytes = sha1.ComputeHash(inputBytes);

                StringBuilder sb = new StringBuilder();
                foreach (var b in hashBytes) {
                    sb.Append(b.ToString("x2"));
                }

                return sb.ToString().ToUpper();
            }
            //throw new NotImplementedException("Andreas please check how fleet API creates the password hash and implement this method");
        }

        var passwordHash = GeneratePasswordHash(password);
        return passwordHash == expectedPasswordHash
            ? PasswordVerificationResult.Success
            : PasswordVerificationResult.Failed;
    }
}