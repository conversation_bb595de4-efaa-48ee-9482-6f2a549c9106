﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Auth.IO.Sql;

public partial class AuthRepository {
    public Task<ClientUser?> GetClientUser(string username, CancellationToken token) {
        return context.ClientUsers
            .Include(c => c.ClientUserTokens)
            .FirstOrDefaultAsync(u => u.UserName == username, token);
    }

    public async Task AddRefreshToken(ClientUser user, string refreshToken) {
        user.ClientUserTokens.Add(new ClientUserToken {
            Token = refreshToken,
            ExpiresTs = DateTime.UtcNow.AddDays(settings.RefreshTokenValidityDays),
            CreatedTs = DateTime.UtcNow,
            UserId = user.UserId,
            ClientUserId = user.ClientUserId
        });

        await context.SaveChangesAsync();
    }
}