﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.Features.RefreshToken;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Auth.IO.Http;

public partial class AuthController {
    [HttpPost("refresh-token")]
    public async Task<ActionResult<RefreshTokenResponse>> RefreshToken(RefreshTokenRequest request) {
        //var authHeader = Request.Headers["Authorization"].FirstOrDefault();
        //string accessToken = null;

        //if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ")) {
        //    accessToken = authHeader.Substring("Bearer ".Length).Trim();
        //}
        //var refreshTokenRequestExtention = new RefreshTokenRequestExtention() { RefreshToken = request.RefreshToken, AccessToken = accessToken };
        var resp = await mediator.Send(request);
        if (resp.Value == null) {
            return this.NotFound(new { Message = $"Invalid refresh token: {request.RefreshToken}" });
        }

        if (resp.IsExpired) {
            return this.NotFound(new { Message = $"Token has expired: {request.RefreshToken}" });
        }

        return this.Ok(resp);
    }
}