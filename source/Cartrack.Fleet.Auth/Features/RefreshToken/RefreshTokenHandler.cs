﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.Features.Login;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using MediatR;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Cartrack.Fleet.Auth.Features.RefreshToken;

public class RefreshTokenHandler(AppSettings settings, IAuthRepository repository, IJwtService jwtService)
    : IRequestHandler<RefreshTokenRequest, RefreshTokenResponse> {
    public async Task<RefreshTokenResponse> Handle(RefreshTokenRequest request, CancellationToken cancellationToken) {
        //bool isMainUser = false;
        ClientUserToken refreshToken = null;
        var principal = jwtService.GetPrincipalFromToken(request.RefreshToken);
        var userName = principal?.FindFirst(ClaimTypes.Name)?.Value;
        var mainUsers = settings.MainUser.Split(',').Select(u => u.Trim());
        if (mainUsers.Any(u => u.Equals(userName, StringComparison.OrdinalIgnoreCase))) {
            var ctUser = await repository.GetUserByUserName(userName);
            if (ctUser is null) {
                return new RefreshTokenResponse(null);
            }
            else {
                var expClaim = principal?.FindFirst(c => c.Type == JwtRegisteredClaimNames.Exp);
                if (expClaim != null && long.TryParse(expClaim.Value, out var expSeconds)) {
                    var expiryUtc = DateTimeOffset.FromUnixTimeSeconds(expSeconds).UtcDateTime;
                    if (DateTime.UtcNow >= expiryUtc.ToUniversalTime()) {
                        return new RefreshTokenResponse(
                            new Token() {
                                AccessToken = "",
                                RefreshToken = request.RefreshToken,
                                ExpiresAt = expiryUtc
                            }) {
                            IsExpired = true
                        };
                    }
                }
                //isMainUser = true;
                refreshToken = new ClientUserToken();
                refreshToken.ClientUser = new ClientUser() {
                    UserName = ctUser.UserName,
                    UserId = ctUser.UserId,
                    ClientUserId = ctUser.UserId.ToString(),
                    PasswordHash = ctUser.PasswordHash
                };
            }
        }
        else {
            refreshToken = await repository.GetRefreshToken(request.RefreshToken);

            if (refreshToken == null) {
                return new RefreshTokenResponse(null);
            }

            if (DateTime.UtcNow >= refreshToken.ExpiresTs.ToUniversalTime()) {
                return new RefreshTokenResponse(
                    new Token() {
                        AccessToken = "",
                        RefreshToken = refreshToken.Token,
                        ExpiresAt = refreshToken.ExpiresTs
                    }) {
                    IsExpired = true
                };
            }
        }
        var user = await repository.GetUser(refreshToken.ClientUser.UserId);

        //var newRefreshToken = jwtService.GenerateRefreshToken(refreshToken.ClientUser, user!.UserName);
        //if (!isMainUser) {
        //    await repository.UpdateRefreshToken(refreshToken, newRefreshToken);
        //}
        // Generate new tokens
        var newAccessToken = jwtService.GenerateAccessToken(refreshToken.ClientUser, user!.UserName);
        return new RefreshTokenResponse(
            new Token() {
                AccessToken = newAccessToken,
                RefreshToken = request.RefreshToken,
                ExpiresAt = DateTime.UtcNow.AddMinutes(settings.TokenValidityMinutes)
            });
    }
}