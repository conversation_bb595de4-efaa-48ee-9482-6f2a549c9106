﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Features.RefreshToken;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Auth.IO.Http;

public partial class AuthController {
    [Authorize]
    [HttpPost("revoke-token")]
    public async Task<ActionResult<RevokeTokenResponse>> RevokeToken(RevokeTokenRequest request) {
        var resp = await mediator.Send(request);
        if (resp.Value == null) {
            return this.NotFound(new {
                Message = $"Invalid refresh token: {request.RefreshToken}"
            });
        }

        if (resp.IsExpired) {
            return this.NotFound(new {
                Message = $"Token has expired: {request.RefreshToken}"
            });
        }

        if (resp.IsUnauthorized) {
            return this.Unauthorized(new {
                Message = $"Invalid refresh token: {request.RefreshToken}"
            });
        }

        return this.Ok(resp);
    }
}