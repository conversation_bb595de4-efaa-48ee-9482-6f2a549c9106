﻿using Cartrack.EFCore.Models.Fleet;
using Cartrack.Fleet.Auth.Domain;
using Cartrack.Fleet.Auth.IO.Sql;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Auth.IO.Sql;

public partial class AuthRepository {
    public async Task RevokeToken(ClientUserToken refreshToken) {
        // Revoke token
        refreshToken.RevokedTs = DateTime.UtcNow;
        refreshToken.ExpiresTs = DateTime.UtcNow.AddMinutes(-1);
        await context.SaveChangesAsync();
    }
}