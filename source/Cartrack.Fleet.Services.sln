﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Audit", "Cartrack.Fleet.Audit\Cartrack.Fleet.Audit.csproj", "{662B5C55-C18A-43C0-8455-1466DEC0E96D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Host", "Cartrack.Fleet.Host\Cartrack.Fleet.Host.csproj", "{7B1AB42F-4C51-4F94-AB1F-39AB484C9161}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Common", "Cartrack.Fleet.Common\Cartrack.Fleet.Common.csproj", "{DB3B47A4-5A04-4D62-833D-BAFFF61BF53F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{74B5FE75-19DA-47CD-92AA-8F551540C23F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Audit.Tests", "..\tests\Cartrack.Fleet.Audit.Tests\Cartrack.Fleet.Audit.Tests.csproj", "{F1C977EB-2B19-4597-90E4-ED51F651A0B3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Booking", "Cartrack.Fleet.Booking\Cartrack.Fleet.Booking.csproj", "{674EBE2B-8525-41A4-BF15-B09FA0A6B783}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Auth", "Cartrack.Fleet.Auth\Cartrack.Fleet.Auth.csproj", "{7A4B8F29-67BF-4FDB-9247-9118DAD7D547}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.User", "Cartrack.Fleet.User\Cartrack.Fleet.User.csproj", "{09239E12-0127-4664-847B-9C3A60A582A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Accessory", "Cartrack.Fleet.Accessory\Cartrack.Fleet.Accessory.csproj", "{FB060635-3901-4508-947D-80CB72D9683C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Cartrack.Fleet.Driver", "Cartrack.Fleet.Driver\Cartrack.Fleet.Driver.csproj", "{78F3827E-1C44-4EB2-9A55-54A77CD043D8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Cartrack.Fleet.Vehicle", "Cartrack.Fleet.Vehicle\Cartrack.Fleet.Vehicle.csproj", "{95172F3E-BEAF-4C58-AE18-99A67D2C1494}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Cartrack.Fleet.License", "Cartrack.Fleet.License\Cartrack.Fleet.License.csproj", "{36B1616C-E082-4278-8562-6CDA71AA53DB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{662B5C55-C18A-43C0-8455-1466DEC0E96D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{662B5C55-C18A-43C0-8455-1466DEC0E96D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{662B5C55-C18A-43C0-8455-1466DEC0E96D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{662B5C55-C18A-43C0-8455-1466DEC0E96D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B1AB42F-4C51-4F94-AB1F-39AB484C9161}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B1AB42F-4C51-4F94-AB1F-39AB484C9161}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B1AB42F-4C51-4F94-AB1F-39AB484C9161}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B1AB42F-4C51-4F94-AB1F-39AB484C9161}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB3B47A4-5A04-4D62-833D-BAFFF61BF53F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB3B47A4-5A04-4D62-833D-BAFFF61BF53F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB3B47A4-5A04-4D62-833D-BAFFF61BF53F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB3B47A4-5A04-4D62-833D-BAFFF61BF53F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1C977EB-2B19-4597-90E4-ED51F651A0B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1C977EB-2B19-4597-90E4-ED51F651A0B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1C977EB-2B19-4597-90E4-ED51F651A0B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1C977EB-2B19-4597-90E4-ED51F651A0B3}.Release|Any CPU.Build.0 = Release|Any CPU
		{674EBE2B-8525-41A4-BF15-B09FA0A6B783}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{674EBE2B-8525-41A4-BF15-B09FA0A6B783}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{674EBE2B-8525-41A4-BF15-B09FA0A6B783}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{674EBE2B-8525-41A4-BF15-B09FA0A6B783}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A4B8F29-67BF-4FDB-9247-9118DAD7D547}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A4B8F29-67BF-4FDB-9247-9118DAD7D547}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A4B8F29-67BF-4FDB-9247-9118DAD7D547}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A4B8F29-67BF-4FDB-9247-9118DAD7D547}.Release|Any CPU.Build.0 = Release|Any CPU
		{09239E12-0127-4664-847B-9C3A60A582A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09239E12-0127-4664-847B-9C3A60A582A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09239E12-0127-4664-847B-9C3A60A582A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09239E12-0127-4664-847B-9C3A60A582A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB060635-3901-4508-947D-80CB72D9683C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB060635-3901-4508-947D-80CB72D9683C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB060635-3901-4508-947D-80CB72D9683C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB060635-3901-4508-947D-80CB72D9683C}.Release|Any CPU.Build.0 = Release|Any CPU
		{78F3827E-1C44-4EB2-9A55-54A77CD043D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78F3827E-1C44-4EB2-9A55-54A77CD043D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78F3827E-1C44-4EB2-9A55-54A77CD043D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78F3827E-1C44-4EB2-9A55-54A77CD043D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{95172F3E-BEAF-4C58-AE18-99A67D2C1494}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95172F3E-BEAF-4C58-AE18-99A67D2C1494}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95172F3E-BEAF-4C58-AE18-99A67D2C1494}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95172F3E-BEAF-4C58-AE18-99A67D2C1494}.Release|Any CPU.Build.0 = Release|Any CPU
		{36B1616C-E082-4278-8562-6CDA71AA53DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36B1616C-E082-4278-8562-6CDA71AA53DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36B1616C-E082-4278-8562-6CDA71AA53DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36B1616C-E082-4278-8562-6CDA71AA53DB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F1C977EB-2B19-4597-90E4-ED51F651A0B3} = {74B5FE75-19DA-47CD-92AA-8F551540C23F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {066F59F6-192E-43FB-82CC-F8E5A31ADEF4}
	EndGlobalSection
EndGlobal
