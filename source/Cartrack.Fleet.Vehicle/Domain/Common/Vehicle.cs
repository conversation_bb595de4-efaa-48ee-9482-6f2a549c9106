﻿namespace Cartrack.Fleet.Vehicle.Domain.Common;

public abstract class Vehicle : IVehicle {
    public long VehicleId { get; set; }
    public string? Registration { get; set; }
    public long UserId { get; set; }
    public string? VehicleName { get; set; } // vehicle_name
    public string? ClientVehicleDescription { get; set; } // client_vehicle_description
    public decimal? FuelTargetConsumption { get; set; } // fuel_target_consumption
    public long? VehicleEngineTypeId { get; set; } // vehicle_engine_type_id
    public short? MaintenanceId { get; set; }
    public long? DefaultSiteLocationId { get; set; }
    public bool? IsPoolActive { get; set; }
    public bool? CommonPool { get; set; }
    public long? MaintenanceStatusId { get; set; }
    public long? BookingVehicleTypeId { get; set; }
    public long? VehicleStatusOptionId { get; set; }
    public decimal? BookingAllocationPriority { get; set; }
    public int? SpeedSourceId { get; set; }
    public string? DefaultDriver { get; set; }
    public int? MonthlyMileageLimit { get; set; }
    public DateTime? CreatedTs { get; set; } // cts
    public DateTime? UpdatedTs { get; set; } // uts
    public List<VehicleDepartment>? Departments { get; set; }
    public List<VehicleQdlLicense>? DriverLicenses { get; set; }
    public List<VehiclePdpLicense>? SpecialDriverLicenses { get; set; }
    public abstract Task Validate();
}