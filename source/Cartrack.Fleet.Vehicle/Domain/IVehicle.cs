﻿using Cartrack.Fleet.Vehicle.Domain.Common;

namespace Cartrack.Fleet.Vehicle.Domain;

public interface IVehicle {
    long VehicleId { get; set; }
    string? Registration { get; set; }
    long UserId { get; set; }
    string? VehicleName { get; set; } // vehicle_name
    string? ClientVehicleDescription { get; set; } // client_vehicle_description
    decimal? FuelTargetConsumption { get; set; } // fuel_target_consumption
    long? VehicleEngineTypeId { get; set; } // vehicle_engine_type_id
    short? MaintenanceId { get; set; }
    long? DefaultSiteLocationId { get; set; }
    bool? IsPoolActive { get; set; }
    long? MaintenanceStatusId { get; set; }
    long? BookingVehicleTypeId { get; set; }
    long? VehicleStatusOptionId { get; set; }
    decimal? BookingAllocationPriority { get; set; }
    int? SpeedSourceId { get; set; }
    string? DefaultDriver { get; set; }
    DateTime? CreatedTs { get; set; } // cts
    DateTime? UpdatedTs { get; set; } // uts
    List<VehicleDepartment>? Departments { get; set; }
    List<VehicleQdlLicense>? DriverLicenses { get; set; }

    List<VehiclePdpLicense>? SpecialDriverLicenses { get; set; }

    //List<VehicleSensor>? Sensors { get; set; }
    Task Validate();
}