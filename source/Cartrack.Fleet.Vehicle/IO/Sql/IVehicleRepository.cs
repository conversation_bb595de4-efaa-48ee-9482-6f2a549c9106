﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.IO.Http;

namespace Cartrack.Fleet.Vehicle.IO.Sql;

public interface IVehicleRepository {
    Task<VehicleBase?> GetVehicleById(long userId, long vehicleId);
    Task<VehicleBase?> GetVehicleByRegistration(int userId, string registrationNumber);
    Task<List<VehicleBase>> GetVehiclesByVehicleGroup(int userId, long vehicleGroupId, int page, int pageSize);
    Task<int> GetTotalVehicleByVehicleGroups(int userId, long vehicleGroupId);
    Task<List<VehicleBase>> GetVehiclesByVehicleCategory(int userId, long bookingVehicleTypeId, int page, int pageSize);
    Task<int> GetTotalVehiclesByVehicleCategory(int userId, long bookingVehicleTypeId);
    Task<List<VehicleGroup>> GetVehicleGroups(int userId, int page, int pageSize);
    Task<int> GetTotalVehicleGroups(int userId);
    Task<List<VehicleCategory>> GetVehicleCategories(int userId, int page, int pageSize);
    Task<int> GetTotalVehicleCategories(int userId);
    Task<List<VehicleBase>> GetVehicles(int userId, int page, int pageSize);

    Task<int> GetTotal(int userId);

    //Task GetVehicleGroups(int userId, long vehicleGroupId);
    Task<List<VehicleDepartment>> GetDepartments(long userId, long vehicleId);
    Task<List<VehiclePdpLicense>> GetPdpLicensesById(int userId, long vehicleId);
    Task<List<VehicleQdlLicense>> GetQdlLicensesById(int userId, long vehicleId);

    Task UpdateVehicle(int userId, long vehicleId, VehicleBase driver);
    //Task DeleteLicenses(int userId, long vehicleId);
    //Task AddLicenses(int userId, long vehicleId);
}