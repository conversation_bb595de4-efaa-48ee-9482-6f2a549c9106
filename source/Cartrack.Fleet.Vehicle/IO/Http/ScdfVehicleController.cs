﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.IO.Http;

[ApiController]
[Route("scdf/vehicle")]
public partial class ScdfVehicleController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<ScdfVehicleController> _logger;
    private readonly IMediator _mediator;

    public ScdfVehicleController(ILogger<ScdfVehicleController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}