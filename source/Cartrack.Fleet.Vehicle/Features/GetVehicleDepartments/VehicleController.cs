﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("departments/{vehicleId:long}")]
    public async Task<ActionResult<GetVehicleDepartmentsResponse>> GetDepartments(long vehicleId = 0) {
        var resp = await this._mediator.Send(new GetVehicleDepartmentsRequest(vehicleId));
        return resp.ToContentResult<GetVehicleDepartmentsResponse, VehicleDepartments>(this.HttpContext);
    }
}