﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration;

public record GetVehicleByRegistrationRequest(string RegistrationNumber) : IRequest<GetVehicleByRegistrationResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF000001";

    [JsonIgnore] public int UserId { get; init; } = 302739;
    //public string ClientDriverId { get; set; }
}