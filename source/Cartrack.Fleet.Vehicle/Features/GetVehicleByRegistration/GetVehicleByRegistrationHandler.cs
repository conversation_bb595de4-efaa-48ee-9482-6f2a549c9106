﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration;

public class GetVehicleByRegistrationHandler(IVehicleRepository repo, ILogger<GetVehicleByRegistrationHandler> logger)
    : IRequestHandler<GetVehicleByRegistrationRequest, GetVehicleByRegistrationResponse> {
    public async Task<GetVehicleByRegistrationResponse> Handle(GetVehicleByRegistrationRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.VehicleId > 0, "Vehicle ID must be greater than zero");
            Requires.NotNullOrEmpty(request.RegistrationNumber, nameof(request.RegistrationNumber));
            logger.LogInformation("[{Agency}] Retrieving the vehicle with Registration Number={VehicleRegistration}",
                request.Account, request.RegistrationNumber);

            var vehicle = await repo.GetVehicleByRegistration(request.UserId, request.RegistrationNumber);
            var apiVehicle = vehicle?.ToHttpVehicle();
            return new GetVehicleByRegistrationResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Vehicle by Registration Number.");
            return new GetVehicleByRegistrationResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Vehicle by Registration Number.");
            return new GetVehicleByRegistrationResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}