﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("registration/{id}")]
    public async Task<ActionResult<GetVehicleByRegistrationResponse>> GetVehicleByRegistration(string id = "FBB1234X") {
        var resp = await this._mediator.Send(new GetVehicleByRegistrationRequest(id));
        return resp.ToContentResult<GetVehicleByRegistrationResponse, Vehicle>(this.HttpContext);
    }
}