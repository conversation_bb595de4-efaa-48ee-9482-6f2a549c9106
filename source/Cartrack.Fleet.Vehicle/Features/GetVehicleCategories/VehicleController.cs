﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleCategories;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("categories")]
    public async Task<ActionResult<GetVehicleCategoriesResponse>> GetVehicleCategories([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetVehicleCategoriesRequest(page, pageSize));
        return resp.ToContentResult<GetVehicleCategoriesResponse, VehicleCategories>(this.HttpContext);
    }
}