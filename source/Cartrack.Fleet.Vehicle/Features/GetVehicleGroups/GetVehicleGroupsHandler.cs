﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Features.GetVehicleGroups;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleGroups;

public class GetVehicleGroupsHandler(IVehicleRepository repo, ILogger<GetVehicleGroupsHandler> logger)
    : IRequestHandler<GetVehicleGroupsRequest, GetVehicleGroupsResponse> {
    public async Task<GetVehicleGroupsResponse> Handle(GetVehicleGroupsRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving all vehicle groups", request.Account);

            var vehicleGroups = await repo.GetVehicleGroups(request.UserId, request.Page, request.PageSize);
            var total = await repo.GetTotalVehicleGroups(request.UserId);
            var apiVehicleGroups = vehicleGroups?.ToHttpVehicleGroups(total);
            return new GetVehicleGroupsResponse(apiVehicleGroups);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Vehicle Group List");
            return new GetVehicleGroupsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Vehicle Group List");
            return new GetVehicleGroupsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}