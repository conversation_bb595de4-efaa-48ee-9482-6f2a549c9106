﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Http;
using Microsoft.EntityFrameworkCore;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<List<VehicleGroup>> GetVehicleGroups(int userId, int page, int pageSize) {
        List<VehicleGroup> clientGroupVehicles = await (from gv in fleetDbContext.GroupVehicles
                where gv.UserId == userId
                join gvl in fleetDbContext.GroupVehicleLinks on gv.GroupVehicleId equals gvl.GroupVehicleId into
                    vehicleGroup
                select new VehicleGroup {
                    Id = gv.GroupVehicleId,
                    Name = gv.Name,
                    Description = gv.Description,
                    VehicleCount = vehicleGroup.Count()
                }
            )
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return clientGroupVehicles;
    }

    public async Task<int> GetTotalVehicleGroups(int userId) {
        return await fleetDbContext.GroupVehicles
            .Where(c => c.UserId == userId)
            .CountAsync();
    }
}