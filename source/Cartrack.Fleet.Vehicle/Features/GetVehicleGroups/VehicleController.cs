﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleGroups;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("groups")]
    public async Task<ActionResult<GetVehicleGroupsResponse>> GetVehicleGroups([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetVehicleGroupsRequest(page, pageSize));
        return resp.ToContentResult<GetVehicleGroupsResponse, VehicleGroups>(this.HttpContext);
    }
}