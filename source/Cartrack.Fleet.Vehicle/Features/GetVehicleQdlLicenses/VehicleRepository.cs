﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class VehicleRepository {
    public async Task<List<VehicleQdlLicense>> GetQdlLicensesById(int userId, long vehicleId) {
        var qdlLicenses = await (from vdl in fleetDbContext.VehicleDriverLicenses
                where vdl.VehicleId == vehicleId
                join dlt in fleetDbContext.DriverLicenseTypes on vdl.DriverLicenseTypeId equals dlt.DriverLicenseTypeId
                    into dltJoin
                from dlt in dltJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                group new {
                    vdl, dlt,
                } by vdl
                into g
                select new {
                    g.Key.VehicleId,
                    g.Key.DriverLicenseTypeId,
                    DriverLicenseType = g.Select(x =>
                        x.dlt != null
                            ? new VehicleQdlLicense() {
                                LicenseTypeId = x.dlt.DriverLicenseTypeId, LicenseName = x.dlt.Name
                            }
                            : null).FirstOrDefault(),
                })
            .ToListAsync();

        List<VehicleQdlLicense> vehicleQdlLicenses = [];
        vehicleQdlLicenses.AddRange(qdlLicenses
            .Where(p => p.DriverLicenseType != null)
            .Select(p => new VehicleQdlLicense() {
                LicenseTypeId = p.DriverLicenseType.LicenseTypeId, LicenseName = p.DriverLicenseType.LicenseName
            })
        );

        return vehicleQdlLicenses;
    }
}