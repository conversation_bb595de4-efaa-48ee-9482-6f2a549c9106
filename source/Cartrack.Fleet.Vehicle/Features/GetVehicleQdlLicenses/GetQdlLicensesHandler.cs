﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses;

public class GetQdlLicensesHandler(IVehicleRepository repo, ILogger<GetQdlLicensesHandler> logger)
    : IRequestHandler<GetVehicleQdlLicensesRequest, GetVehicleQdlLicensesResponse> {
    public async Task<GetVehicleQdlLicensesResponse> Handle(GetVehicleQdlLicensesRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Id > 0, "Accessory ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving the QDL Licenses with VehicleId={VehicleId}", request.Account,
                request.VehicleId);

            var vehicle = await repo.GetQdlLicensesById(request.UserId, request.VehicleId);
            var apiVehicle = vehicle?.ToHttpQdlLicenses();
            return new GetVehicleQdlLicensesResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Vehicle QDL Licenses");
            return new GetVehicleQdlLicensesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Vehicle QDL Licenses");
            return new GetVehicleQdlLicensesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}