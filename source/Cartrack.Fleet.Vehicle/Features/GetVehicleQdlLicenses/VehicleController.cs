﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("qdllicenses/{vehicleId:long}")]
    public async Task<ActionResult<GetVehicleQdlLicensesResponse>> GetQdlLicenses(long vehicleId) {
        var resp = await this._mediator.Send(new GetVehicleQdlLicensesRequest(vehicleId));
        return resp.ToContentResult<GetVehicleQdlLicensesResponse, VehicleQdlLicenses>(this.HttpContext);
    }
}