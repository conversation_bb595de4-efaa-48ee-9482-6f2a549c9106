﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory;

public record GetVehiclesByVehicleCategoryRequest(long VehicleCategoryId, int Page, int PageSize)
    : IRequest<GetVehiclesByVehicleCategoryResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF000001";

    [JsonIgnore] public int UserId { get; init; } = 302739;
    //public string ClientDriverId { get; set; }
}