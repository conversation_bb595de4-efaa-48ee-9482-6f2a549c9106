﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("category/{vehicleCategoryId:long}")]
    public async Task<ActionResult<GetVehiclesByVehicleCategoryResponse>> GetVehiclesByVehicleCategory(
        long vehicleCategoryId, [FromQuery] int page = 1, [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(
            new GetVehiclesByVehicleCategoryRequest(vehicleCategoryId, page, pageSize));
        return resp.ToContentResult<GetVehiclesByVehicleCategoryResponse, Vehicles>(this.HttpContext);
    }
}