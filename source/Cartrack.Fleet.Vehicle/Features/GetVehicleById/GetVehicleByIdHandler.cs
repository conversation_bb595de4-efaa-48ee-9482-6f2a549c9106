﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicleById;

public class GetVehicleByIdHandler(IVehicleRepository repo, ILogger<GetVehicleByIdHandler> logger)
    : IRequestHandler<GetVehicleByIdRequest, GetVehicleByIdResponse> {
    public async Task<GetVehicleByIdResponse>
        Handle(GetVehicleByIdRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.VehicleId > 0, "Vehicle ID must be greater than zero");
            //Requires.NotNullOrEmpty(request.VehicleId, nameof(request.Id));
            logger.LogInformation("[{Agency}] Retrieving the vehicle with Id={VehicleId}", request.Account,
                request.VehicleId);

            var vehicle = await repo.GetVehicleById(request.UserId, request.VehicleId);
            var apiVehicle = vehicle?.ToHttpVehicle();
            return new GetVehicleByIdResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Vehicle");
            return new GetVehicleByIdResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Vehicle");
            return new GetVehicleByIdResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}