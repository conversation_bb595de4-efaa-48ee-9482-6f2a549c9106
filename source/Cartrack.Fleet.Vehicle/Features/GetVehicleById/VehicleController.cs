﻿using Cartrack.Fleet.Vehicle.Features.GetVehicleById;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("id/{id}")]
    public async Task<ActionResult<GetVehicleByIdResponse>> GetDriverById(long id = 0) {
        var resp = await this._mediator.Send(new GetVehicleByIdRequest(id));
        return resp.ToContentResult<GetVehicleByIdResponse, Vehicle>(this.HttpContext);
    }
}