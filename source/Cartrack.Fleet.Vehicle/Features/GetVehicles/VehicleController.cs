﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.GetVehicles;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    public async Task<ActionResult<GetVehiclesResponse>> GetVehicles([FromQuery] int page = 1,
        [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetVehiclesRequest(page, pageSize));
        return resp.ToContentResult<GetVehiclesResponse, Vehicles>(this.HttpContext);
    }
}