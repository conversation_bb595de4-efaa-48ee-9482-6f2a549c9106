﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.GetVehicles;

public class GetVehiclesHandler(IVehicleRepository repo, ILogger<GetVehiclesHandler> logger)
    : IRequestHandler<GetVehiclesRequest, GetVehiclesResponse> {
    public async Task<GetVehiclesResponse> Handle(GetVehiclesRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving all vehicles", request.Account);

            var vehicles = await repo.GetVehicles(request.UserId, request.Page, request.PageSize);
            var total = await repo.GetTotal(request.UserId);
            var apiVehicle = vehicles?.ToHttpVehicles(total);
            return new GetVehiclesResponse(apiVehicle);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving Vehicles List");
            return new GetVehiclesResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving Vehicles List");
            return new GetVehiclesResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}