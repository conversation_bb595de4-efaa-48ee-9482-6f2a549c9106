﻿using Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Domain;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("pdplicenses/{vehicleId:long}")]
    public async Task<ActionResult<GetVehiclePdpLicensesResponse>> GetPdpLicenses(long vehicleId) {
        var resp = await this._mediator.Send(new GetVehiclePdpLicensesRequest(vehicleId));
        return resp.ToContentResult<GetVehiclePdpLicensesResponse, VehiclePdpLicenses>(this.HttpContext);
    }
}