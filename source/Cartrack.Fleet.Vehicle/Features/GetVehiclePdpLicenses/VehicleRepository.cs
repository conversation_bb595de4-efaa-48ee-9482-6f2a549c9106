﻿using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Sql;

public partial class VehicleRepository {
    public async Task<List<VehiclePdpLicense>> GetPdpLicensesById(int userId, long vehicleId) {
        var pdpLicenses = await (from vsl in fleetDbContext.VehicleSpecialLicenses
                where vsl.VehicleId == vehicleId
                join dslt in fleetDbContext.DriverSpecialLicenseTypes on vsl.SpecialLicenseTypeId equals dslt
                    .DriverSpecialLicenseTypeId into dsltJoin
                from dslt in dsltJoin.Where(x => x.UserId == userId || x.UserId == null).DefaultIfEmpty()
                group new {
                    vsl, dslt,
                } by vsl
                into g
                select new {
                    g.Key.VehicleId,
                    g.Key.SpecialLicenseTypeId,
                    DriverSpecialLicenseType = g.Select(x =>
                        x.dslt != null
                            ? new VehiclePdpLicense() {
                                LicenseTypeId = x.dslt.DriverSpecialLicenseTypeId, LicenseName = x.dslt.LicenseName
                            }
                            : null).FirstOrDefault(),
                })
            .ToListAsync();

        List<VehiclePdpLicense> vehiclePdpLicenses = [];
        vehiclePdpLicenses.AddRange(pdpLicenses
            .Where(p => p.DriverSpecialLicenseType != null)
            .Select(p => new VehiclePdpLicense() {
                LicenseTypeId = p.DriverSpecialLicenseType.LicenseTypeId, LicenseName = p.DriverSpecialLicenseType.LicenseName,
            })
        );

        return vehiclePdpLicenses;
    }
}