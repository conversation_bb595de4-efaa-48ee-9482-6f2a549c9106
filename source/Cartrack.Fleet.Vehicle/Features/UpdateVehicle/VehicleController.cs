﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.UpdateVehicle;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpPatch]
    [Route("{vehicleId:long}")]
    public async Task<ActionResult<UpdateVehicleResponse>> UpdateVehicle(long vehicleId,
        [FromBody] UpdateVehicleRequest request) {
        request.VehicleId = vehicleId;
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<UpdateVehicleResponse, Vehicle>(this.HttpContext);
    }
}