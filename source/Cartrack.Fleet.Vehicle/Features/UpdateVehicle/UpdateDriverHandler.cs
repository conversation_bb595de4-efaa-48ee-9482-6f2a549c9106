﻿using Cartrack.AppHost;
using Cartrack.Fleet.Vehicle.Domain;
using Cartrack.Fleet.Vehicle.Domain.Common;
using Cartrack.Fleet.Vehicle.IO;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Vehicle.Features.UpdateVehicle;

public class UpdateDriverHandler(IVehicleRepository repo, ILogger<UpdateDriverHandler> logger)
    : IRequestHandler<UpdateVehicleRequest, UpdateVehicleResponse> {
    public async Task<UpdateVehicleResponse> Handle(UpdateVehicleRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            //Requires.NotNullOrEmpty(request.VehicleId, nameof(request.VehicleId));
            logger.LogInformation("[{Agency}] Updating a vehicle #{VehicleId}", request.Account, request.VehicleId);

            VehicleBase vehicle = await CreateFromRequest(request);
            await repo.UpdateVehicle(request.UserId, request.VehicleId, vehicle);
            var updatedVehicle = await repo.GetVehicleById(request.UserId, request.VehicleId);
            return new UpdateVehicleResponse(updatedVehicle?.ToHttpVehicle());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error updating vehicle. ");
            return new UpdateVehicleResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error updating vehicle.");
            return new UpdateVehicleResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private static async Task<VehicleBase> CreateFromRequest(UpdateVehicleRequest request) {
        //1. Get AppSetting needed to update Driver

        // Department Setup
        List<VehicleDepartment> vehicleDepartments = [];
        if (request.DepartmentIds is not null) {
            vehicleDepartments.AddRange(request.DepartmentIds.Select(dl => new VehicleDepartment {
                Id = dl
            }));
        }

        // Normal Driver Licenses Setup
        List<VehicleQdlLicense> listQdlLicenses = [];
        if (request.DriverLicenseIds is not null) {
            listQdlLicenses.AddRange(
                request.DriverLicenseIds.Select(dl => new VehicleQdlLicense {
                    LicenseTypeId = dl,
                }));
        }

        // Special Driver Licenses Setup
        List<VehiclePdpLicense> listPdpLicenses = [];
        if (request.SpecialDriverLicenseIds is not null) {
            listPdpLicenses.AddRange(request.SpecialDriverLicenseIds.Select(dl => new VehiclePdpLicense {
                LicenseTypeId = dl,
            }));
        }

        //2. Create Base Driver
        var vehicle = new VehicleBase() {
            UserId = request.UserId,
            VehicleId = request.VehicleId,
            UpdatedTs = DateTime.UtcNow,
        };
        if (request.VehicleName != null) { vehicle.VehicleName = request.VehicleName; }

        if (request.ClientVehicleDescription != null) {
            vehicle.ClientVehicleDescription = request.ClientVehicleDescription;
        }

        if (request.FuelTargetConsumption != null) { vehicle.FuelTargetConsumption = request.FuelTargetConsumption; }

        if (request.VehicleEngineTypeId != null) { vehicle.VehicleEngineTypeId = request.VehicleEngineTypeId; }

        if (request.VehicleCategoryId != null) { vehicle.BookingVehicleTypeId = request.VehicleCategoryId; }

        if (request.DefaultDriver != null) { vehicle.DefaultDriver = request.DefaultDriver; }

        if (request.DefaultSiteLocationId != null) { vehicle.DefaultSiteLocationId = request.DefaultSiteLocationId; }

        if (request.BookingAllocationPriority != null) {
            vehicle.BookingAllocationPriority = request.BookingAllocationPriority;
        }

        if (request.VehicleStatusOptionId != null) { vehicle.VehicleStatusOptionId = request.VehicleStatusOptionId; }

        if (request.DepartmentIds != null) { vehicle.Departments = vehicleDepartments; }

        if (request.DriverLicenseIds != null) { vehicle.DriverLicenses = listQdlLicenses; }

        if (request.SpecialDriverLicenseIds != null) { vehicle.SpecialDriverLicenses = listPdpLicenses; }

        if (request.IsPoolActive != null) { vehicle.IsPoolActive = request.IsPoolActive; }

        if (request.MonthlyMileageLimit != null) { vehicle.MonthlyMileageLimit = request.MonthlyMileageLimit; }

        //2. Validate the accessory
        //await vehicle.Validate();

        return vehicle;
    }
}