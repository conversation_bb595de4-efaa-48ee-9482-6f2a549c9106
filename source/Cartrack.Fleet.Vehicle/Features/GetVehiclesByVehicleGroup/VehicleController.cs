﻿using Cartrack.Fleet.Common;
using Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Vehicle.IO.Http;

public partial class VehicleController {
    [HttpGet]
    [Route("group/{vehicleGroupId:long}")]
    public async Task<ActionResult<GetVehiclesByVehicleGroupResponse>> GetVehicleByVehicleGroup(long vehicleGroupId,
        [FromQuery] int page = 1, [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetVehiclesByVehicleGroupRequest(vehicleGroupId, page, pageSize));
        return resp.ToContentResult<GetVehiclesByVehicleGroupResponse, Vehicles>(this.HttpContext);
    }
}