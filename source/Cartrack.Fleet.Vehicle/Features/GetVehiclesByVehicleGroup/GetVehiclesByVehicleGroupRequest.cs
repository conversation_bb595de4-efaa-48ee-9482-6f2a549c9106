﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup;

public record GetVehiclesByVehicleGroupRequest(long VehicleGroupId, int Page, int PageSize)
    : IRequest<GetVehiclesByVehicleGroupResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF000001";

    [JsonIgnore] public int UserId { get; init; } = 302739;
    //public string ClientDriverId { get; set; }
}