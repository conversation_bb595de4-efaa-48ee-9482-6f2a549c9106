﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfActivateBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository rule,
    IDriverRepository driverRepo,
    IVehicleRepository vehicleRepo,
    ILogger<ScdfActivateBookingHandler> logger)
    : IRequestHandler<ScdfActivateBookingRequest, ScdfActivateBookingResponse> {
    public async Task<ScdfActivateBookingResponse> Handle(ScdfActivateBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account ?? "", nameof(request.Account));
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.IsTrue(() => request.PickUpTime is { } time, "Pickup Time is Required.");
            //Requires.IsTrue(() => request.ActualPickUpTime >= request.PickUpTime, "Actual Pickup Time must be greater than Pickup Time.");
            logger.LogInformation("[{Agency}] Activating a booking", request.Account);

            ScdfVehicleBooking booking = await this.ActivateFromRequest(request);
            await booking?.Status.Activate()!;
            var bookingId = await repo.ActivateBooking(booking);
            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfActivateBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error Activate booking");
            return new ScdfActivateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error Activate booking");
            return new ScdfActivateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<ScdfVehicleBooking> ActivateFromRequest(ScdfActivateBookingRequest request) {
        var getCurrentBooking = await repo.GetBooking(request.Account, request.BookingId);
        Requires.IsTrue(() => getCurrentBooking != null, "Booking Id does not exist in the system.");

        var userId = getCurrentBooking.UserId;

        var vehicleInfo = await vehicleRepo.GetVehicleById(userId, request.VehicleId);
        Requires.IsTrue(() => vehicleInfo != null, "Vehicle Id does not exist in the system.");

        var vehicle = new VehicleTarget {
            Vehicle = new Domain.Common.Vehicle {
                VehicleId = request.VehicleId,
                Registration = vehicleInfo!.Registration ?? "",
                CommonPool = vehicleInfo.CommonPool ?? false,
                CarpoolEnabled = vehicleInfo.IsPoolActive ?? false
            }
        };

        var getDriverObj = await driverRepo.GetDriverById(userId, request.ClientDriverId);
        Requires.IsTrue(() => getDriverObj != null, "Driver is not exist in the system.");

        var driver = new Domain.Common.Driver {
            DriverId = request.ClientDriverId,
            DriverName = getDriverObj!.FirstName,
            DriverSurname = getDriverObj.Surname,
            DriverDepartments = await driverRepo.GetDepartmentsByDriverId(userId, request.ClientDriverId),
            DriverQdlLicense = await driverRepo.GetQdlLicensesById(request.ClientDriverId),
            DriverPdpLicense = await driverRepo.GetPdpLicensesById(request.ClientDriverId),
            Email = getDriverObj.Email
        };

        var booking = new ScdfVehicleBooking {
            BookingId = request.BookingId,
            VehicleId = request.VehicleId,
            BookingStatusId = BookingStatusCode.Active,
            StartDate = DateTime.SpecifyKind(request.PickUpTime, DateTimeKind.Utc),
            EndDate = getCurrentBooking.EndDate,
            PickupTime = DateTime.SpecifyKind(request.ActualPickUpTime, DateTimeKind.Utc),
            Status = getCurrentBooking!.Status,
            Driver = driver,
            Target = vehicle,
            UpdatedDate = DateTime.UtcNow,
            VehicleCommanderClientUserId = request.VehicleCommanderClientUserId,
            RequestClientDriverId = request.ClientDriverId,
            ActivatedClientUserId = request.ActivatedClientUserId,
            ActivationTrigger = new ManualActivationTrigger()
        };

        foreach (var r in await this.GetScdfBookingRules(userId)) {
            if (r.IsEnabled == true) {
                booking.PreConditions.Add(r);
            }
        }

        booking.IsBookingTimeConflicting = await this.IsBookingTimeAvailable(booking);
        booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowed(booking);

        await booking.Validate();

        return booking;
    }

    private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
        var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

        List<IBookingRule> bookingRule = [];
        foreach (var r in userBookingRule) {
            switch (r.BookingRuleId) {
                case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeIsDriverRequired:
                    bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
            }
        }

        //ADD THE SCDF-specific rules
        bookingRule.Add(new MustHaveVehicleRule(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) {
            IsEnabled = true
        });

        bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
            IsEnabled = true
        });

        return bookingRule;
    }
    private async Task<bool> IsBookingTimeAvailable(VehicleBookingBase booking) {
        bool result = await repo.IsBookingTimeConflicting(booking);
        return result;
    }

    private async Task<bool> IsVehicleCommonPoolRuleAllowed(VehicleBookingBase booking) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
        bool result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool, clientDriverDepartments, vehicleDepartments);
        return result;
    }
}