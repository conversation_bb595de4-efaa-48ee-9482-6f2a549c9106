﻿using Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("activate")]
    public async Task<ActionResult<ScdfActivateBookingResponse>> ActivateBooking([FromBody] ScdfActivateBookingRequest request) {
        
        //request.ActivateClientUserId = this.User.FindFirst(ClaimTypes.NameIdentifier)!.Value;
        //request.Account = this.User.FindFirst("Account")!.Value;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfActivateBookingResponse, Booking>(this.HttpContext);
    }
}