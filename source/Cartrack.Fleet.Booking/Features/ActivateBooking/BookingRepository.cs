﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public virtual async Task<long> ActivateBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }

        if (booking?.BookingStatusId != null) {
            myBooking.BookingStatusId = (long)booking.BookingStatusId;
        }
        
        myBooking.VehicleId = booking?.VehicleId;
        myBooking.UpdatedTs = booking?.UpdatedDate;
        myBooking.PickupIgnitionTs = booking?.PickupTime;
        myBooking.StartTs = booking!.StartDate;
        myBooking.RequestClientDriverId = booking.RequestClientDriverId;
        myBooking.ActivatedTs = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
        myBooking.ActivatedClientUserId = booking.ActivatedClientUserId;
        
        await poolDbContext.SaveChangesAsync();
       
        return bookingId;
    }
}