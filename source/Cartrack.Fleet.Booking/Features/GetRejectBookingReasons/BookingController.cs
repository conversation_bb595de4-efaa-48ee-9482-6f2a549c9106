﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetRejectBookingReasons;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("reject-booking-reasons")]
    public async Task<ActionResult<GetRejectBookingReasonsResponse>> GetRejectBookingReasons() {
        var resp = await this._mediator.Send(new GetRejectBookingReasonsRequest());
        return resp.ToContentResult<GetRejectBookingReasonsResponse, RejectBookingReasons>(this.HttpContext);
    }
}