﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetRejectBookingReasons;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<RejectBookingReason>>
        GetRejectBookingReasons(long userId, long rejectBookingReasonId) {
        
        var query = poolDbContext.BookingRejectReasons
            .Where(x => x.UserId == userId && !x.IsDeleted);

        if (rejectBookingReasonId != 0) {
            query = query.Where(x => x.BookingRejectReasonId == rejectBookingReasonId);
        }
        
        var purposes = await query.ToListAsync() ?? null;
        
        if (purposes is null) {
            return new List<RejectBookingReason>();
        }

        List<RejectBookingReason> output = purposes.Select(p => new RejectBookingReason {
            Id = p.BookingRejectReasonId,
            Title = p.BookingRejectReason1 ?? string.Empty,
            Description = ""
        }).ToList();

        return output;
    }
}