﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("requestpurpose-vehicle-categories-map")]
    public async Task<ActionResult<GetRequestPurposeVehicleCategoriesMapResponse>> GetRequestPurposeVehicleCategoriesMap([FromQuery] int page = 1, [FromQuery] int pageSize = 10) {
        var resp = await this._mediator.Send(new GetRequestPurposeVehicleCategoriesMapRequest(0, page, pageSize));
        return resp.ToContentResult<GetRequestPurposeVehicleCategoriesMapResponse, RequestPurposeVehicleCategoryMaps>(this.HttpContext);
    }
}