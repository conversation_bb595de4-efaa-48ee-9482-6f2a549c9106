﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap;

public class
    GetRequestPurposeVehicleCategoriesMapHandler(IBookingRepository repo, ILogger<GetRequestPurposeVehicleCategoriesMapHandler> logger)
    : IRequestHandler<GetRequestPurposeVehicleCategoriesMapRequest, GetRequestPurposeVehicleCategoriesMapResponse> {
    public async Task<GetRequestPurposeVehicleCategoriesMapResponse> Handle(GetRequestPurposeVehicleCategoriesMapRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            logger.LogInformation("[{Agency}] Retrieving request purposes - vehicle categories map", request.Account);

            var result = await repo.GetRequestPurposeVehicleCategoriesMaps(request.UserId, request.BookingPurposeId, request.Page, request.PageSize);
            var total = await repo.GetRequestPurposeVehicleCategoriesMapsTotal(request.UserId);
            var apiRequestPurposes = result?.ToHttpRequestPurposeVehicleCategoriesMap(total);
            return new GetRequestPurposeVehicleCategoriesMapResponse(apiRequestPurposes);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving request purpose - vehicle categories map");
            return new GetRequestPurposeVehicleCategoriesMapResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving request purpose - vehicle categories map");
            return new GetRequestPurposeVehicleCategoriesMapResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}