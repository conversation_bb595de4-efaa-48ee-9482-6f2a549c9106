﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBooking.Spf;

public class SpfGetBookingHandler(IBookingRepository repo, ILogger<SpfGetBookingHandler> logger)
    : IRequestHandler<SpfGetBookingRequest, SpfGetBookingResponse> {
    public async Task<SpfGetBookingResponse> Handle(SpfGetBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.Id > 0, "Booking ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Retrieving the booking with Id={BookingId}", request.Account, request.Id);

            var booking = await repo.GetBooking(request.Account, request.Id);
            var apiBooking = booking?.ToHttpBooking();
            return new SpfGetBookingResponse(apiBooking);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking");
            return new SpfGetBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking");
            return new SpfGetBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}