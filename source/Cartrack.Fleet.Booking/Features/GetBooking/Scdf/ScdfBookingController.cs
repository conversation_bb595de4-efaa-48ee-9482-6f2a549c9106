using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpGet]
    [Route("{id}")]
    public async Task<ActionResult<ScdfGetBookingResponse>> GetBooking(long id) {
        var resp = await this._mediator.Send(new ScdfGetBookingRequest(id));
        return resp.ToContentResult<ScdfGetBookingResponse, Booking>(this.HttpContext);
    }
}