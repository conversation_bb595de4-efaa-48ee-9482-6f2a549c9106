﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

//----------------------------------------------------
//Put all the common repository functions in this file
//----------------------------------------------------
public partial class BookingRepository {

    public Task<VehicleBookingBase?> GetBooking(IVehicleBookingBuilder builder, long id) {
        return builder.Build(id);
    }
    public async Task<VehicleBookingBase?> GetBooking(string agency, long id) {
        var ctUser = await ctDbContext.Users.FirstOrDefaultAsync(c => c.UserName.Equals(agency));
        if (ctUser is null) {
            return null;
        }
        
        // Uncomment later when we finish the authentication function
        // var booking = await poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == id && b.UserId == ctUser.UserId);
        var booking = await poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == id);
        if (booking is null) {
            return null;
        }

        var builder = bookingBuilder(agency);
        return await GetBooking(builder, id);
    }
}