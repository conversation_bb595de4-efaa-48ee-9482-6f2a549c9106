﻿using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    public async Task<ActionResult<ScdfGetBookingsResponse>> GetBookings(
        [FromBody] ServerRequestModel serverRequestModel = null) {
        var request = new ScdfGetBookingsRequest {
            /*DateRange =
                startDate != null || endDate != null
                    ? new DateRangeFilter { StartDate = startDate, EndDate = endDate }
                    : null,
            StatusIds = parsedStatusIds,
            Sort = new SortOptions { Column = sortColumn ?? "requestedTime", Direction = sortDirection ?? "desc" },*/
            // Account = "SCDF00001", // TODO: remove this when have authentication
            ServerRequestModal = serverRequestModel
        };

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfGetBookingsResponse, Booking[]>(this.HttpContext);
    }
}