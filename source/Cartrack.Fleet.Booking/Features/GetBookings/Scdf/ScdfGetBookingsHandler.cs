﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public class ScdfGetBookingsHandler(ScdfBookingRepository repo, ILogger<ScdfGetBookingsHandler> logger) : IRequestHandler<ScdfGetBookingsRequest, ScdfGetBookingsResponse> {
    public async Task<ScdfGetBookingsResponse> Handle(ScdfGetBookingsRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.ServerRequestModal.Pagination.Offset >= 0,
                "Offset must be greater than or equal to zero");
            Requires.IsTrue(() => request.ServerRequestModal.Pagination.PageSize > 0,
                "Page size must be greater than zero");
            // Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            // this._logger.LogInformation("[{Agency}] Retrieving bookings with criteria", request.Account);

            var result = await repo.GetBookings(
                "SCDF00001", // TODO: remove this when have authentication
                request.ServerRequestModal);

            var httpBookings = result.Bookings.Select(b => b.ToHttpBooking()).ToArray();

            var statusMetrics = result.StatusCounts
                .Select(kvp => new StatusCount { StatusId = kvp.Key, Count = kvp.Value })
                .ToArray();

            return new ScdfGetBookingsResponse(
                httpBookings,
                new PaginationMetrics(result.Total, statusMetrics)
            );
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving bookings");
            return new ScdfGetBookingsResponse(null, new PaginationMetrics(0, Array.Empty<StatusCount>()), ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving bookings");
            return new ScdfGetBookingsResponse(null, new PaginationMetrics(0, Array.Empty<StatusCount>()), ex) {
                IsServerError = true
            };
        }
    }
}