﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;

namespace Cartrack.Fleet.Booking.Features.GetBookings.Scdf;

public record ScdfGetBookingsResponse : BaseResponse<IO.Http.Booking[]> {
    public PaginationMetrics Metrics { get; init; }

    public ScdfGetBookingsResponse(IO.Http.Booking[]? value, PaginationMetrics metrics, Exception? error = null)
        : base(value, error) {
        Metrics = metrics;
    }
}

public record PaginationMetrics {
    public int Total { get; init; }
    public StatusCount[] StatusMetrics { get; init; }

    public PaginationMetrics(int total, StatusCount[] statusMetrics) {
        Total = total;
        StatusMetrics = statusMetrics;
    }
}

public record StatusCount {
    public long StatusId { get; init; }
    public int Count { get; init; }
}