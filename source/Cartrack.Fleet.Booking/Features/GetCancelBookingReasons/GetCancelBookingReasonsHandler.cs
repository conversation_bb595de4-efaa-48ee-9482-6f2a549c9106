﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetCancelBookingReasons;

public class
    GetCancelBookingReasonsHandler(IBookingRepository repo, ILogger<GetCancelBookingReasonsHandler> logger)
    : IRequestHandler<GetCancelBookingReasonsRequest, GetCancelBookingReasonsResponse> {
    public async Task<GetCancelBookingReasonsResponse> Handle(GetCancelBookingReasonsRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            logger.LogInformation("[{Agency}] Retrieving cancel booking reason lists", request.Account);

            var result = await repo.GetCancelBookingReasons(request.UserId, request.CancelBookingReasonId);

            var apiCancelReasons = result?.ToHttpCancelBookingReasons();
            return new GetCancelBookingReasonsResponse(apiCancelReasons);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving cancel booking reason lists");
            return new GetCancelBookingReasonsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving cancel booking reason lists");
            return new GetCancelBookingReasonsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}