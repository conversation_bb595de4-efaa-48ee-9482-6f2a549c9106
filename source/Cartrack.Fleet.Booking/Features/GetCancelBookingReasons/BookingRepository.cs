﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetRejectBookingReasons;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<CancelBookingReason>>
        GetCancelBookingReasons(long userId, long cancelBookingReasonId) {
        
        var query = poolDbContext.BookingCancelReasons
            .Where(x => x.UserId == userId && !x.IsDeleted);

        if (cancelBookingReasonId != 0) {
            query = query.Where(x => x.BookingCancelReasonId == cancelBookingReasonId);
        }
        
        var purposes = await query.ToListAsync() ?? null;
        
        if (purposes is null) {
            return new List<CancelBookingReason>();
        }

        List<CancelBookingReason> output = purposes.Select(p => new CancelBookingReason {
            Id = p.BookingCancelReasonId,
            Title = p.BookingCancelReason1 ?? string.Empty,
            Description = ""
        }).ToList();

        return output;
    }
}