﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetCancelBookingReasons;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("cancel-booking-reasons")]
    public async Task<ActionResult<GetCancelBookingReasonsResponse>> GetCancelBookingReasons() {
        var resp = await this._mediator.Send(new GetCancelBookingReasonsRequest());
        return resp.ToContentResult<GetCancelBookingReasonsResponse, CancelBookingReasons>(this.HttpContext);
    }
}