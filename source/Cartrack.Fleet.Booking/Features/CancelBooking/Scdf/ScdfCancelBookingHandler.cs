﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;

public class ScdfCancelBookingHandler(ScdfBookingRepository repo, ILogger<ScdfCancelBookingHandler> logger)
    : IRequestHandler<ScdfCancelBookingRequest, ScdfCancelBookingResponse> {
    public async Task<ScdfCancelBookingResponse> Handle(ScdfCancelBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            // Validation
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.bookingId > 0, "Booking Id must be greater than zero");
            Requires.IsTrue(() => request.bookingCancelReasonId > 0, "Booking Cancel ReasonId must be greater than zero");
            Requires.NotNullOrEmpty(request.bookingCancelNotes, nameof(request.bookingCancelNotes));

            logger.LogInformation("[{Agency}] Cancel booking", request.Account);

            List<long> bIds = [];
            var booking = await repo.GetBooking(request.Account, request.bookingId);
            if (booking?.BookingId == 0) {
                bIds = [request.bookingId];
                return new ScdfCancelBookingResponse(
                    new BookingResponseStatus(bIds, false, "Cancel skipped: This booking doesn't exist"));
            }

            await booking?.Status.Cancel()!;

            var cancelReason = await repo.GetBookingCancelReason(request.Account, request.bookingCancelReasonId);
            if (cancelReason is null) {
                bIds = [request.bookingCancelReasonId];
                return new ScdfCancelBookingResponse(
                    new BookingResponseStatus(bIds, false,
                        "Cancel skipped: This booking cancel reason id is not valid"));
            }

            await repo.CancelBooking(booking.BookingId, request.canceledClientUserId, request.bookingCancelNotes);

            bIds = [request.bookingId];
            return new ScdfCancelBookingResponse(
                new BookingResponseStatus(bIds, true, "Booking has been canceled"));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error cancelling booking");
            return new ScdfCancelBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Unexpected error cancelling booking");
            return new ScdfCancelBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}