﻿using Cartrack.Fleet.Booking.Features.CancelBooking.Scdf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("cancel")]
    public async Task<ActionResult<ScdfCancelBookingResponse>> CancelBooking(
        [FromBody] ScdfCancelBookingRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfCancelBookingResponse, BookingResponseStatus>(this.HttpContext);
    }
}