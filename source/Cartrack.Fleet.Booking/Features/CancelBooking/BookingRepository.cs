﻿using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task CancelBooking(long bookingId, string canceledClientUserId, string bookingCancelNotes) {
        var existsBooking = await poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);
        if (existsBooking is not null) {
            existsBooking.BookingStatusId = (long)BookingStatusCode.Cancelled;
            existsBooking.CanceledTs = DateTime.UtcNow;
            existsBooking.CanceledClientUserId = canceledClientUserId;
            existsBooking.BookingCancelNotes = bookingCancelNotes;
            poolDbContext.Update(existsBooking);
            await poolDbContext.SaveChangesAsync();
        }

        ;
    }

    public async Task<BookingCancelReasonBase> GetBookingCancelReason(string agency, long cancelReasonId) {
        Console.WriteLine($"[{agency}] Getting booking for {cancelReasonId}");

        var ctUser = await ctDbContext.Users.FirstOrDefaultAsync(c => c.UserName.Equals(agency));
        if (ctUser is null) {
            return null;
        }

        var bookingCancelReason = await poolDbContext.BookingCancelReasons
            .FirstOrDefaultAsync(b => b.BookingCancelReasonId == cancelReasonId && b.UserId == ctUser.UserId);
        if (bookingCancelReason != null) {
            var bookingCancelReasonbase = new BookingCancelReasonBase();
            bookingCancelReasonbase.BookingCancelReasonId = bookingCancelReason.BookingCancelReasonId;
            bookingCancelReasonbase.UserId = bookingCancelReason.UserId;
            bookingCancelReasonbase.BookingCancelReason1 = bookingCancelReason.BookingCancelReason1;
            bookingCancelReasonbase.IsDeleted = bookingCancelReason.IsDeleted;
            bookingCancelReasonbase.InternalDescription = bookingCancelReason.InternalDescription;
            return bookingCancelReasonbase;
        }
        else {
            return null;
        }
    }
}