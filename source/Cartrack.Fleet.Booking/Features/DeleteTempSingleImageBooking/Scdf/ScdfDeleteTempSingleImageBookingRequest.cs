﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf;

public record ScdfDeleteTempSingleImageBookingRequest : IRequest<ScdfDeleteTempSingleImageBookingResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF00001";

    [JsonIgnore] public long UserId { get; init; } = 302739;
    public string? Guid { get; set; }
    public string? Extension { get; set; }
}