﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf;

public class ScdfDeleteTempSingleImageBookingHandler(
    ScdfBookingRepository repo,
    ILogger<ScdfDeleteTempSingleImageBookingHandler> logger)
    : IRequestHandler<ScdfDeleteTempSingleImageBookingRequest, ScdfDeleteTempSingleImageBookingResponse> {
    public async Task<ScdfDeleteTempSingleImageBookingResponse> Handle(ScdfDeleteTempSingleImageBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Deleting temporary single image from server", request.Account);

            var tempAttachment = await repo.DeleteTempUploadSingleImage(request.Guid, request.Extension);

            var statusHttp = new TempDeleteImageStatus(
                tempAttachment.TempFileAttachmentGuid ?? "",
                tempAttachment.Extension ?? "",
                "OK");

            return new ScdfDeleteTempSingleImageBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error uploading temporary single image to server");
            return new ScdfDeleteTempSingleImageBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error uploading temporary single image");
            return new ScdfDeleteTempSingleImageBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(ScdfDeleteTempSingleImageBookingRequest request) {
        throw new NotImplementedException();
    }
}