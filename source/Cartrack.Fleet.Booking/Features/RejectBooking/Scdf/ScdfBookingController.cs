﻿using Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("reject")]
    public async Task<ActionResult<ScdfRejectBookingResponse>> RejectBooking([FromBody] ScdfRejectBookingRequest request) {
        
        //request.RejectClientUserId = this.User.FindFirst(ClaimTypes.NameIdentifier)!.Value;
        //request.Account = this.User.FindFirst("Account")!.Value;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfRejectBookingResponse, Booking>(this.HttpContext);
    }
}