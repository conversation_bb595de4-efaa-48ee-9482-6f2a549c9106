﻿using Cartrack.Fleet.Booking.Domain.Common;
using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;

// ReSharper disable once IdentifierTypo
public record ScdfRejectBookingRequest() : IRequest<ScdfRejectBookingResponse> {
    [JsonIgnore] public string? Account { get; init; } = "SCDF00001";
    public long BookingId { get; set; }
    public string? RejectClientUserId { get; set; }
    public long BookingRejectReasonId { get; init; }
    public string BookingRejectReason { get; init; } = string.Empty;
}