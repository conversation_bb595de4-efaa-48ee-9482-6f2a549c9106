﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;
using Journey = Cartrack.Fleet.Booking.Domain.Common.Journey;
using Vehicle = Cartrack.Fleet.Booking.Domain.Common.Vehicle;

namespace Cartrack.Fleet.Booking.Features.RejectBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfRejectBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository rule,
    ILogger<ScdfRejectBookingHandler> logger)
    : IRequestHandler<ScdfRejectBookingRequest, ScdfRejectBookingResponse> {
    public async Task<ScdfRejectBookingResponse> Handle(ScdfRejectBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingId != null || request.BookingId != 0, "Booking Id is Required.");
            logger.LogInformation("[{Agency}] Rejecting a booking", request.Account);

            ScdfVehicleBooking booking = await this.RejectFromRequest(request);
            await booking?.Status.Reject()!;
            var bookingId = await repo.RejectBooking(booking);
            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfRejectBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error reject booking");
            return new ScdfRejectBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error reject booking");
            return new ScdfRejectBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<ScdfVehicleBooking> RejectFromRequest(ScdfRejectBookingRequest request) {
        var getCurrentBooking = await repo.GetBooking(request.Account, request.BookingId);
        
        var rejectedBy = new List<ClientUser> {
            new ClientUser {
                ClientUserId = request.RejectClientUserId,
                Username = ""
            }
        };

        var booking = new ScdfVehicleBooking {
            BookingId = request.BookingId,
            BookingStatusId = BookingStatusCode.Rejected,
            Status = getCurrentBooking!.Status,
            UpdatedDate = DateTime.UtcNow,
            RejectedBy = rejectedBy,
            BookingRejectReasonId = request.BookingRejectReasonId,
            BookingRejectReason = request.BookingRejectReason
        };

        /*foreach (var r in await this.GetScdfBookingRules(request.UserId)) {
            if (r.IsEnabled == true) {
                booking.PreConditions.Add(r);
            }
        }*/
        
        await booking.Validate();

        return booking;
    }

    /*private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
        List<IBookingRule> bookingRule = [];
        return bookingRule;
    }*/
}