﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<long> UpdateBooking(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }

        if (booking?.BookingStatusId != null) {
            myBooking.BookingStatusId = (long)booking.BookingStatusId;
        }

        if (booking?.VehicleId != null) {
            myBooking.VehicleId = booking.VehicleId;
        }

        myBooking.BookingVehicleTypeId = booking.BookingVehicleTypeId;
        myBooking.BookingPurposeId = booking.BookingPurposeId;
        myBooking.BookingPurposeDescription = booking.BookingPurposeDescription;
        myBooking.PickupSiteLocationId = booking.PickupSiteLocationId ?? 0;
        if (booking.JourneyType == JourneyType.Return) {
            myBooking.ReturnSiteLocationId = booking.PickupSiteLocationId ?? 0;
        }

        myBooking.StartTs = booking.StartDate;
        myBooking.EndTs = booking.EndDate;
        myBooking.RequestClientDriverId = booking.RequestClientDriverId;
        myBooking.RequestClientUserId = booking.RequestClientUserId;
        //myBooking.Requestor = booking.Requestor; // username, must catch in JWT
        myBooking.Remarks = booking.Remarks;

        await poolDbContext.SaveChangesAsync();

        // Edit into SCDF Custom Booking Additional Info
        var b = await tfmsCustomDbContext.ScdfBookingAdditionalInfos.FirstOrDefaultAsync(b => b.BookingId == bookingId);

        b.PassengerCount = booking.NumberOfPassengers;
        b.VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId;
        b.RequestedForClientUserId = booking.RequestedForClientUserId;
        b.LocationType = (int)booking.JourneyType;
        if (booking.DriverType1 != null) { b.DriverType = (int)booking.DriverType1; }

        if (booking.VehicleCommanderType1 != null) { b.VehicleCommanderType = (int)booking.VehicleCommanderType1; }

        ;
        if (booking.EquipmentType1 != null) { b.EquipmentType = (int)booking.EquipmentType1; }

        await tfmsCustomDbContext.SaveChangesAsync();

        // Journey Related
        await this.AddOrUpdateBookingJourneys(myBooking.UserId, bookingId, booking.Journeys);

        // Accessories Related
        if (booking.Accessories is { Count: > 0 }) {
            await this.AddOrUpdateBookingAccessory(bookingId, booking.Accessories);
        }

        // Booking Attachment
        // Existing Booking Attachment Ids
        if (booking.EquipmentAttachmentIds != null) {
            await this.UpdateBookingEquipmentAttachmentIds(bookingId, booking.EquipmentAttachmentIds);
        }

        // New Booking Attachments
        if (booking.EquipmentAttachments is { Count: > 0 }) {
            await this.AddBookingEquipmentAttachments(bookingId, booking.EquipmentAttachments);
        }

        return bookingId;
    }
}