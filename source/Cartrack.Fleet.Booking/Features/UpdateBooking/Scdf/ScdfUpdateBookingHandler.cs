﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;
using BookingAccessory = Cartrack.Fleet.Booking.Domain.Common.BookingAccessory;

namespace Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf;

public class ScdfUpdateBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository rule,
    IDriverRepository driverRepo,
    ILogger<ScdfUpdateBookingHandler> logger) : IRequestHandler<ScdfUpdateBookingRequest, ScdfUpdateBookingResponse> {
    public async Task<ScdfUpdateBookingResponse> Handle(ScdfUpdateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingPurposeId != null || request.BookingPurposeId != 0,
                "Purpose of Request is Required.");
            Requires.IsTrue(() => request.BookingId != null || request.BookingId != 0, "Booking Id is Required.");
            Requires.IsTrue(() => request.EndTs is { } time, "End Date is Required.");
            Requires.IsTrue(() => request.StartTs is { } time, "Start Date is Required.");
            Requires.IsTrue(() => request.EndTs >= request.StartTs, "End Date must be greater than Start Date.");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Updating a booking", request.Account);

            var booking1 = await repo.GetBooking(request.Account, request.BookingId);
            await booking1?.Status.Request()!;

            ScdfVehicleBooking booking = await this.UpdateFromRequest(request);
            var bookingId = await repo.UpdateBooking(booking);

            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfUpdateBookingResponse(updatedBooking.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value in update booking. Parameter is Required");
            return new ScdfUpdateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error update booking");
            return new ScdfUpdateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<ScdfVehicleBooking> UpdateFromRequest(ScdfUpdateBookingRequest request) {
        List<Journey> journeys = new List<Journey>();
        int journeyOrder = 1;
        foreach (var r in request.Journeys) {
            journeys.Add(new Journey {
                LocationReference = new JourneyLocation {
                    Type = r.Type, Value = r.Value
                },
                Order = journeyOrder,
            });
            journeyOrder++;
        }

        var vehicle = new VehicleTarget {
            Vehicle = new Domain.Common.Vehicle {
                VehicleId = 0, Registration = ""
            }
        };

        var getDriverObj = await driverRepo.GetDriverById(request.UserId, request.RequestClientDriverId);
        Domain.Common.Driver driver = null;
        if (getDriverObj != null) {
            driver = new Domain.Common.Driver {
                DriverId = request.RequestClientDriverId,
                DriverName = getDriverObj!.FirstName,
                DriverSurname = getDriverObj.Surname,
                DriverDepartments = await driverRepo.GetDepartmentsByDriverId(request.UserId, request.RequestClientDriverId),
                DriverQdlLicense = await driverRepo.GetQdlLicensesById(request.RequestClientDriverId),
                DriverPdpLicense = await driverRepo.GetPdpLicensesById(request.RequestClientDriverId),
                Email = ""
            };
        }

        //1. Create all the booking rules that applies to SCDF Booking
        var booking = new ScdfVehicleBooking {
            BookingId = request.BookingId,
            UserId = request.UserId,
            CancellationTrigger = null,
            VehicleId = null,
            Driver = driver,
            BookingVehicleTypeId = request.BookingVehicleTypeId,
            BookingStatusId = BookingStatusCode.Requested,
            BookingPurposeId = request.BookingPurposeId,
            BookingPurposeDescription = request.BookingPurposeDescription,
            StartDate = DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc),
            EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc),
            DriverType1 = request.DriverType,
            RequestClientDriverId = request.RequestClientDriverId,
            VehicleCommanderType1 = request.VehicleCommanderType,
            VehicleCommanderClientUserId = request.VehicleCommanderClientUserId,
            PickupSiteLocationId = request.PickupSiteLocationId,
            ReturnSiteLocationId = (request.JourneyType == JourneyType.Return ? request.PickupSiteLocationId : 0),
            Description = null,
            Purpose = null,
            Target = null,
            CreatedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            //UpdatedDate = null,
            Type = BookingType.Standard,
            RequestedForClientUserId = request.RequestedForClientUserId,
            RequestClientUserId = request.RequestClientUserId,
            RequestedBy = null, // client_user_id
            RequestedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            //ApprovedBy = null,
            //RejectedBy = null,
            ActivationTrigger = null,
            Remarks = request.Remarks ?? "",
            NumberOfPassengers = request.NumberOfPassengers,
            EquipmentType1 = request.EquipmentType,
            Accessories = request.Accessories ?? null,
            JourneyType = request.JourneyType,
            Journeys = journeys,

            // Equipment Attachment 
            EquipmentAttachmentIds = request.EquipmentAttachmentIds, // for existing attachment
            EquipmentAttachments = request.EquipmentAttachments, // for new attachments
        };

        foreach (var r in await this.GetScdfBookingRules(request.UserId)) {
            if (r.IsEnabled == true) {
                booking.PreConditions.Add(r);
            }
        }

        booking.IsBookingTimeConflicting = await this.IsBookingTimeConflicting(booking);
        booking.IsBookingPurposeOthersRequired = await this.IsBookingPurposeOthersRequired(booking);

        await booking.Validate();

        return booking;
    }

    /*private List<IBookingRule> GetCommonBookingRules() {
        return [];
    }*/

    private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
        var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

        List<IBookingRule> bookingRule = [];

        foreach (var r in userBookingRule) {
            switch (r.BookingRuleId) {
                case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeIsDriverRequired:
                    bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeMaximumBookingTime:
                    bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeBookInAdvanceBy:
                    bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
            }
        }

        //ADD THE SCDF-specific rules
        bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHavePassengers(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHavePickupLocation(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveValidJourneys(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
            IsEnabled = true
        });

        return bookingRule;
    }

    private async Task<bool> IsBookingTimeConflicting(VehicleBookingBase booking) {
        bool result = await repo.IsBookingTimeConflicting(booking);
        return result;
    }

    private async Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking) {
        bool result = await repo.IsBookingPurposeOthersRequired(booking);
        return result;
    }
}