﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetRequestPurposes;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("requestpurposes")]
    public async Task<ActionResult<GetRequestPurposesResponse>> GetRequestPurposes() {
        var resp = await this._mediator.Send(new GetRequestPurposesRequest());
        return resp.ToContentResult<GetRequestPurposesResponse, RequestPurposes>(this.HttpContext);
    }
}