﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("force-terminate-booking-reasons")]
    public async Task<ActionResult<GetForceTerminateBookingReasonsResponse>> GetForceTerminateBookingReasons() {
        var resp = await this._mediator.Send(new GetForceTerminateBookingReasonsRequest());
        return resp.ToContentResult<GetForceTerminateBookingReasonsResponse, ForceTerminateBookingReasons>(this.HttpContext);
    }
}