﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons;

public class
    GetForceTerminateBookingReasonsHandler(IBookingRepository repo, ILogger<GetForceTerminateBookingReasonsHandler> logger)
    : IRequestHandler<GetForceTerminateBookingReasonsRequest, GetForceTerminateBookingReasonsResponse> {
    public async Task<GetForceTerminateBookingReasonsResponse> Handle(GetForceTerminateBookingReasonsRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            logger.LogInformation("[{Agency}] Retrieving force-terminate booking reason lists", request.Account);

            var result = await repo.GetForceTerminateBookingReasons(request.UserId, request.ForceTerminateBookingReasonId);

            var apiForceTerminateReasons = result?.ToHttpForceTerminateBookingReasons();
            return new GetForceTerminateBookingReasonsResponse(apiForceTerminateReasons);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving force-terminate booking reason lists");
            return new GetForceTerminateBookingReasonsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving force-terminate booking reason lists");
            return new GetForceTerminateBookingReasonsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}