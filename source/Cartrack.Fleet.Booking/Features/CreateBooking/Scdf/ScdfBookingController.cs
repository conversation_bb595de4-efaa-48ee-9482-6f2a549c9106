﻿using Cartrack.Fleet.Booking.Features.CreateBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("create")]
    public async Task<ActionResult<ScdfCreateBookingResponse>> CreateBooking(
        [FromBody] ScdfCreateBookingRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfCreateBookingResponse, Booking>(this.HttpContext);
    }
}