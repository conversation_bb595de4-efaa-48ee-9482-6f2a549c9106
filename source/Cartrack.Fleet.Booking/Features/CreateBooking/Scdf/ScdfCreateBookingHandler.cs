﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;
using BookingAccessory = Cartrack.Fleet.Booking.Domain.Common.BookingAccessory;

namespace Cartrack.Fleet.Booking.Features.CreateBooking.Scdf;

public class ScdfCreateBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository rule,
    ILogger<ScdfCreateBookingHandler> logger) : IRequestHandler<ScdfCreateBookingRequest, ScdfCreateBookingResponse> {
    public async Task<ScdfCreateBookingResponse> Handle(ScdfCreateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.BookingPurposeId != null || request.BookingPurposeId != 0,
                "Purpose of Request is Required.");
            Requires.IsTrue(() => request.EndTs is { } time, "End Date is Required.");
            Requires.IsTrue(() => request.StartTs is { } time, "Start Date is Required.");
            Requires.IsTrue(() => request.EndTs >= request.StartTs, "End Date must be greater than Start Date.");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Creating a new booking", request.Account);

            ScdfVehicleBooking booking = await this.CreateFromRequest(request);
            var bookingId = await repo.CreateBooking(booking);
            var createdBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfCreateBookingResponse(createdBooking.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Parameter is Required");
            return new ScdfCreateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error creating booking");
            return new ScdfCreateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<ScdfVehicleBooking> CreateFromRequest(ScdfCreateBookingRequest request) {
        List<Journey> journeys = new List<Journey>();
        int journeyOrder = 1;
        foreach (var r in request.Journeys) {
            journeys.Add(new Journey {
                LocationReference = new JourneyLocation {
                    Type = r.Type, Value = r.Value
                },
                Order = journeyOrder,
            });
            journeyOrder++;
        }

        // Booking Approval by Manager if enabled.
        BookingStatusCode bsc = BookingStatusCode.Requested;
        const bool isAutoApproveEnabled = false; // must check from Permission in SubUser level, currently , let set it to false for now.
        if (isAutoApproveEnabled) {
            bsc = BookingStatusCode.Approved;
        }

        if (string.IsNullOrEmpty(request.RequestClientUserId)) {
            var clientUserId = await repo.GetClientUserById(request.RequestClientUserId);
        }

        //1. Create all the booking rules that applies to SCDF Booking
        var booking = new ScdfVehicleBooking {
            UserId = request.UserId,
            CancellationTrigger = null,
            BookingStatusId = bsc,
            BookingPurposeId = request.BookingPurposeId,
            BookingPurposeDescription = request.BookingPurposeDescription,
            BookingVehicleTypeId = request.BookingVehicleTypeId,
            StartDate = DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc),
            EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc),
            DriverType1 = request.DriverType,
            RequestClientDriverId = request.RequestClientDriverId,
            VehicleCommanderType1 = request.VehicleCommanderType,
            VehicleCommanderClientUserId = request.VehicleCommanderClientUserId,
            PickupSiteLocationId = request.PickupSiteLocationId,
            ReturnSiteLocationId = 0, // in SCDF not used. All will be in 'Journeys'
            Description = null,
            Purpose = null,
            Target = null,
            CreatedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            //UpdatedDate = null,
            Type = BookingType.Standard,
            RequestedForClientUserId = request.RequestedForClientUserId,
            RequestClientUserId = request.RequestClientUserId,
            RequestedBy = null, // client_user_id
            RequestedDate = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc),
            Requestor = request.Requestor ?? string.Empty,
            IsAutoApproveEnabled = isAutoApproveEnabled,
            //ApprovedBy = null,
            //RejectedBy = null,
            ActivationTrigger = null,
            Remarks = request.Remarks ?? "",
            NumberOfPassengers = request.NumberOfPassengers,
            EquipmentType1 = request.EquipmentType,
            Accessories = request.Accessories ?? null,
            JourneyType = request.JourneyType,
            Journeys = journeys,
            EquipmentAttachments = request.EquipmentAttachments,
        };

        booking.RequestedClientUserId = await repo.GetClientUserById(request.RequestClientUserId);

        foreach (var r in await this.GetScdfBookingRules(request.UserId)) {
            if (r.IsEnabled == true) {
                booking.PreConditions.Add(r);
            }
        }

        booking.IsBookingTimeConflicting = await this.IsBookingTimeAvailable(booking);
        booking.IsBookingPurposeOthersRequired = await this.IsBookingPurposeOthersRequired(booking);

        await booking.Validate();

        return booking;
    }

    /*private List<IBookingRule> GetCommonBookingRules() {
        return [];
    }*/

    private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
        var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);

        List<IBookingRule> bookingRule = [];

        foreach (var r in userBookingRule) {
            switch (r.BookingRuleId) {
                case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeIsDriverRequired:
                    bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeMaximumBookingTime:
                    bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeBookInAdvanceBy:
                    bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
            }
        }

        // Main Booking Rules
        bookingRule.Add(new MustHaveValidRequestClientUserIdRule(bookingSettings) {
            IsEnabled = true
        });

        //ADD THE SCDF-specific rules
        bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHavePassengers(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHavePickupLocation(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveValidJourneys(bookingSettings) {
            IsEnabled = true
        });
        bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
            IsEnabled = true
        });

        return bookingRule;
    }

    private async Task<bool> IsBookingTimeAvailable(VehicleBookingBase booking) {
        bool result = await repo.IsBookingTimeConflicting(booking);
        return result;
    }

    private async Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking) {
        bool result = await repo.IsBookingPurposeOthersRequired(booking);
        return result;
    }
}