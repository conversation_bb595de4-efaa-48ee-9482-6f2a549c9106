﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.CreateBooking.Spf;

public class SpfCreateBookingHandler(IBookingRepository repo, ILogger<SpfCreateBookingHandler> logger)
    : IRequestHandler<SpfCreateBookingRequest, SpfCreateBookingResponse> {
    public async Task<SpfCreateBookingResponse> Handle(SpfCreateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Creating a booking", request.Account);

            VehicleBookingBase booking = await this.CreateFromRequest(request);
            var bookingId = await repo.CreateBooking(booking);
            var createdBooking = await repo.GetBooking(request.Account, bookingId);
            return new SpfCreateBookingResponse(createdBooking.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error creating booking");
            return new SpfCreateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error creating booking");
            return new SpfCreateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<SpfVehicleBooking> CreateFromRequest(SpfCreateBookingRequest request) {
        //1. Create all the booking rules that applies to SPF
        var booking = new SpfVehicleBooking();
        foreach (var r in this.GetCommonBookingRules()) {
            booking.PreConditions.Add(r);
        }

        foreach (var r in this.GetSpfBookingRules()) {
            booking.PreConditions.Add(r);
        }

        //2. Add the journey. For SPF, StartLocation == DropOffLocation
        //booking.Journeys.Add(new Journey()); 

        //3. Validate the booking.  Execute all the pre-conditions
        await booking.Validate();

        return booking;
    }

    private List<IBookingRule> GetCommonBookingRules() {
        return [];
    }

    private List<IBookingRule> GetSpfBookingRules() {
        return [];
    }
}