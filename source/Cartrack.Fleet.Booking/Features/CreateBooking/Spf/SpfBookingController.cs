﻿using Cartrack.Fleet.Booking.Features.CreateBooking.Spf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class SpfBookingController {
    [HttpPost]
    public async Task<ActionResult<SpfCreateBookingResponse>>
        CreateBooking([FromBody] SpfCreateBookingRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<SpfCreateBookingResponse, Booking>(this.HttpContext);
    }
}