﻿using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<long> CreateBooking(VehicleBookingBase booking) {
        string uuidBookingReference = Guid.NewGuid().ToString();
        var bookingMap = new EFCore.Models.Pool.Booking() {
            UserId = booking.UserId,
            VehicleId = 0,
            BookingStatusId = (int)booking.BookingStatusId,
            BookingReference = uuidBookingReference,
            BookingVehicleTypeId = booking.BookingVehicleTypeId,
            BookingPurposeId = booking.BookingPurposeId,
            BookingPurposeDescription = booking.BookingPurposeDescription,
            BookingCancelReasonId = 0,
            BookingCancelNotes = null,
            PickupSiteLocationId = booking.PickupSiteLocationId ?? 0,
            ReturnSiteLocationId = 0,
            StartTs = booking.StartDate,
            EndTs = booking.EndDate,
            RequestTs = booking.RequestedDate,
            RequestClientDriverId = booking.RequestClientDriverId,
            RequestClientUserId = booking.RequestClientUserId,
            IsApproved = false,
            Requestor = booking.Requestor,
            Remarks = booking.Remarks,
            BookingType = booking.Type switch {
                BookingType.Standard => 0,
                _ => 0
            },
        };

        if (booking.JourneyType == JourneyType.Return) {
            bookingMap.ReturnSiteLocationId = booking.PickupSiteLocationId ?? 0;
        }

        //await poolDbContext.Bookings.AddAsync(bookingMap);
        //await poolDbContext.SaveChangesAsync();
        //long bookingId = bookingMap.BookingId;
        //Console.WriteLine($"New Booking ID: {bookingId}");

        //string monthYearBookingTable = booking.StartDate.ToString("yyyyMM");
        // ReSharper disable once UseRawString
        // ReSharper disable once ConvertToConstant.Local
        string sqlQuery = @"INSERT INTO pool.booking(                    
                    user_id, 
                    vehicle_id,
                    booking_status_id, 
                    booking_vehicle_type_id,     
                    booking_reference,
                    booking_purpose_id, 
                    booking_purpose_description, 
                    booking_cancel_reason_id,
                    booking_cancel_notes,
                    pickup_site_location_id,
                    return_site_location_id,
                                    start_ts,
                                    end_ts,
                                    request_ts,
                                    request_client_driver_id,
                                    request_client_user_id,
                                    is_approved,
                                    requestor,
                                    booking_type,
                                    remarks) 
                VALUES (
                    @UserId, 
                    @VehicleId,
                    @BookingStatusId, 
                    @BookingVehicleTypeId,    
                    @BookingReference,
                    @BookingPurposeId,
                    @BookingPurposeDescription,
                    @BookingCancelReasonId,
                    @BookingCancelNotes,    
                        @PickupSiteLocationId,
                        @ReturnSiteLocationId,
                        @StartTs,
                        @EndTs,
                        @RequestTs,
                        @RequestClientDriverId,
                        @RequestClientUserId,
                        @IsApproved,
                        @Requestor,
                        @BookingType,
                        @Remarks) 
                 ";

        object[] parameters = [
            new NpgsqlParameter("@UserId", booking.UserId),
            new NpgsqlParameter("@VehicleId", bookingMap.VehicleId),
            new NpgsqlParameter("@BookingStatusId", bookingMap.BookingStatusId),
            new NpgsqlParameter("@BookingVehicleTypeId", bookingMap.BookingVehicleTypeId),
            new NpgsqlParameter("@BookingReference", uuidBookingReference),
            new NpgsqlParameter("@BookingPurposeId", bookingMap.BookingPurposeId),
            new NpgsqlParameter("@BookingPurposeDescription",
                bookingMap.BookingPurposeDescription ?? (object)DBNull.Value),
            new NpgsqlParameter("@BookingCancelReasonId", bookingMap.BookingCancelReasonId),
            new NpgsqlParameter("@BookingCancelNotes", bookingMap.BookingCancelNotes ?? (object)DBNull.Value),
            new NpgsqlParameter("@PickupSiteLocationId", bookingMap.PickupSiteLocationId),
            new NpgsqlParameter("@ReturnSiteLocationId", bookingMap.ReturnSiteLocationId),
            new NpgsqlParameter("@StartTs", bookingMap.StartTs),
            new NpgsqlParameter("@EndTs", bookingMap.EndTs),
            new NpgsqlParameter("@RequestTs", bookingMap.RequestTs),
            new NpgsqlParameter("@RequestClientDriverId", bookingMap.RequestClientDriverId ?? (object)DBNull.Value),
            new NpgsqlParameter("@RequestClientUserId", bookingMap.RequestClientUserId ?? (object)DBNull.Value),
            new NpgsqlParameter("@IsApproved", bookingMap.IsApproved),
            new NpgsqlParameter("@Requestor", bookingMap.Requestor ?? (object)DBNull.Value),
            new NpgsqlParameter("@BookingType", (long?)BookingType.Standard ?? (object)DBNull.Value),
            new NpgsqlParameter("@Remarks", booking.Remarks ?? (object)DBNull.Value)
        ];

        await poolDbContext.Database.ExecuteSqlRawAsync(sqlQuery, parameters);

        long bookingId = await poolDbContext.Bookings
            .Where(b => b.BookingReference != null && b.BookingReference.Equals(uuidBookingReference))
            .Select(c => c.BookingId).FirstOrDefaultAsync();
        
        booking.BookingId = bookingId;
        
        // need to clean-up BookingReference
        var bookingById = await poolDbContext.Bookings
            .Where(a => a.BookingId == bookingId)
            .FirstOrDefaultAsync();
        if (bookingById != null) {
            bookingById.BookingReference = null;
        }

        /////////////////
        // Booking Metric
        /////////////////
        /*var bookingMetric = new EFCore.Models.Pool.BookingMetric() {
            BookingId = bookingId,
        };
        await poolDbContext.BookingMetrics.AddAsync(bookingMetric);
        await poolDbContext.SaveChangesAsync();*/

        // Adding into SCDF Custom Booking Additional Info
        var scdfBookingAdditionalInfo = new ScdfBookingAdditionalInfo {
            BookingId = bookingId,
            PassengerCount = booking.NumberOfPassengers,
            VehicleCommanderClientUserId = booking.VehicleCommanderClientUserId,
            RequestedForClientUserId = booking.RequestedForClientUserId,
            LocationType = (int)booking.JourneyType,
            DriverType = (int)booking.DriverType1!,
            VehicleCommanderType = (int)booking.VehicleCommanderType1!,
            EquipmentType = (int)booking.EquipmentType1!,
        };
        await tfmsCustomDbContext.ScdfBookingAdditionalInfos.AddAsync(scdfBookingAdditionalInfo);
        await tfmsCustomDbContext.SaveChangesAsync();

        // Journey Related
        await this.AddOrUpdateBookingJourneys(bookingMap.UserId, bookingId, booking.Journeys);

        // Accessories Related
        if (booking.Accessories is { Count: > 0 }) {
            await this.AddOrUpdateBookingAccessory(bookingId, booking.Accessories);
        }

        // Booking Attachment
        if (booking.EquipmentAttachments is { Count: > 0 }) {
            await this.AddBookingEquipmentAttachments(bookingId, booking.EquipmentAttachments);
        }

        // Create Booking Approval
        //await this.CreateBookingMetric(booking);
        //await this.CreateBookingApproval(booking);

        return bookingId;

    }
}