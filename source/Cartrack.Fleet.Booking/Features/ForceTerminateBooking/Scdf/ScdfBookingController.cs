﻿using Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPatch]
    [Route("forceTerminate")]
    public async Task<ActionResult<ScdfForceTerminateBookingResponse>> ForceTerminateBooking(
        [FromBody] ScdfForceTerminateBookingRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfForceTerminateBookingResponse, BookingResponseStatus>(this.HttpContext);
    }
}