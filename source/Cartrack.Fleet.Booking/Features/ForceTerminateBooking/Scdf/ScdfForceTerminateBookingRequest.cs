﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;

public record ScdfForceTerminateBookingRequest : IRequest<ScdfForceTerminateBookingResponse> {
    [JsonIgnore]
    public string Account { get; init; } = "SCDF00001";
    [JsonIgnore]
    public long UserId { get; init; } = 302739;
    public List<long> BookingIds { get; set; }
    public int BookingForceTerminateReasonId{get;set;}
    public string BookingForceTerminateNotes{get;set;}
    public string ForceTerminateClientUserId{get;set;}
    
}