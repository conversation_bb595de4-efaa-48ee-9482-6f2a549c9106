﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Infratructure;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf;

public class ScdfForceTerminateBookingHandler(ScdfBookingRepository repo, ILogger<ScdfForceTerminateBookingHandler> logger)
    : IRequestHandler<ScdfForceTerminateBookingRequest, ScdfForceTerminateBookingResponse> {
    public async Task<ScdfForceTerminateBookingResponse> Handle(ScdfForceTerminateBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            // Validation
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingIds.Count > 0, "Booking Ids must have at least one booking id");
            Requires.IsTrue(() => request.BookingForceTerminateReasonId > 0, "Please select one Booking Cancel Reason ");
            Requires.NotNullOrEmpty(request.BookingForceTerminateNotes, nameof(request.BookingForceTerminateNotes));

            logger.LogInformation("[{Agency}] Force Terminate booking", request.Account);

            var forceTerminateReason = await repo.GetForceTerminateBookingReasons(request.UserId, request.BookingForceTerminateReasonId);
            Requires.IsTrue(() => forceTerminateReason != null, "Please select one Booking Cancel Reason ");
            
            foreach(var bookingId in request.BookingIds) 
            {
                var booking = await repo.GetBooking(request.Account, bookingId);
                if (booking?.BookingId == 0) {
                    List<long> bIds = [bookingId];
                    return new ScdfForceTerminateBookingResponse(
                        new BookingResponseStatus(bIds, false, $"Force Terminate skipped: This BookingId doesn't exist - {bookingId}"));
                }
                
                await booking?.Status.Terminate()!;
            }

            await repo.ForceTerminateBooking(request.BookingIds, request.ForceTerminateClientUserId, request.BookingForceTerminateReasonId, request.BookingForceTerminateNotes);

            return new ScdfForceTerminateBookingResponse(
                new BookingResponseStatus(request.BookingIds, true, "Booking(s) has been Force Terminated"));
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error force terminate booking(s)");
            return new ScdfForceTerminateBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Unexpected error force terminate booking(s)");
            return new ScdfForceTerminateBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}