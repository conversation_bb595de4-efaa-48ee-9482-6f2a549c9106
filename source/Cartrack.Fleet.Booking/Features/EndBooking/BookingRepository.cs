﻿using Cartrack.EFCore.Models.Pool;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Npgsql;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<long> EndBookingScdf(VehicleBookingBase booking) {
        long bookingId = booking.BookingId;
        var myBooking = await poolDbContext.Bookings.FindAsync(bookingId);
        if (myBooking == null) {
            return -1;
        }

        if (booking?.BookingStatusId != null) {
            myBooking.BookingStatusId = (long)booking.BookingStatusId;
        }
        
        myBooking.VehicleId = booking?.VehicleId;
        myBooking.UpdatedTs = DateTime.UtcNow;
        myBooking.EndTs = booking.EndDate;
        myBooking.ReturnedClientDriverId = booking?.RequestClientDriverId;
        myBooking.ReturnedClientUserId = booking?.ReturnedClientUserId;
        myBooking.ReturnedIgnitionTs = booking?.ReturnedIgnitionTs;
        myBooking.CompletedTs = DateTime.UtcNow;
        myBooking.CompletedClientUserId = booking?.CompletedClientUserId;
        
        await poolDbContext.SaveChangesAsync();

        // ReSharper disable once IdentifierTypo
        var scdfBookingInfo = await tfmsCustomDbContext.ScdfBookingAdditionalInfos.Where(x => x.BookingId == bookingId).FirstOrDefaultAsync();
        if (scdfBookingInfo == null) {
            return bookingId;
        }

        scdfBookingInfo.VehicleCommanderClientUserId = booking?.VehicleCommanderClientUserId;
        scdfBookingInfo.ReturnedVehicleCommanderClientUserId = booking?.ReturnedVehicleCommanderClientUserId;
        await tfmsCustomDbContext.SaveChangesAsync();

        return bookingId;
    }
}