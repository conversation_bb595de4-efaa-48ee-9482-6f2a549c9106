﻿using Cartrack.Fleet.Booking.Features.EndBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("completed")]
    public async Task<ActionResult<ScdfEndBookingResponse>> ScdfEndBooking([FromBody] ScdfEndBookingRequest request) {
        
        //request.RejectClientUserId = this.User.FindFirst(ClaimTypes.NameIdentifier)!.Value;
        //request.Account = this.User.FindFirst("Account")!.Value;
        
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfEndBookingResponse, Booking>(this.HttpContext);
    }
}