﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

// ReSharper disable once IdentifierTypo
namespace Cartrack.Fleet.Booking.Features.EndBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfEndBookingHandler( 
    IBookingRepository repo, 
    IBookingRuleRepository rule,
    IDriverRepository driverRepo, 
    IVehicleRepository vehicleRepo,
    ILogger<ScdfEndBookingHandler> logger) : IRequestHandler<ScdfEndBookingRequest, ScdfEndBookingResponse> {
    public async Task<ScdfEndBookingResponse> Handle(ScdfEndBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingId != null || request.BookingId != 0, "Booking Id is Required.");
            logger.LogInformation("[{Agency}] Complete a booking", request.Account);

            ScdfVehicleBooking booking = await this.CompletedFromRequest(request);

            if (request.ActualDropOffTime > request.DropOffTime) {
                await booking?.Status.ReturnLate()!;
            }
            else {
                await booking?.Status.Return()!;
            }
            
            var bookingId = await repo.EndBookingScdf(booking);
            var updatedBooking = await repo.GetBooking(request.Account, bookingId);
            return new ScdfEndBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error complete/end booking");
            return new ScdfEndBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error complete/end booking");
            return new ScdfEndBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private async Task<ScdfVehicleBooking> CompletedFromRequest(ScdfEndBookingRequest request) {
        var getCurrentBooking = await repo.GetBooking(request.Account, request.BookingId);
        Requires.IsTrue(() => getCurrentBooking != null , "Booking must be exist.");
        
        var vehicleInfo = await vehicleRepo.GetVehicleById(getCurrentBooking.UserId, request.VehicleId);
        Requires.IsTrue(() => vehicleInfo != null, "Vehicle Id does not exist in the system.");
        
        var vehicle = new VehicleTarget {
            Vehicle = new Domain.Common.Vehicle {
                VehicleId = request.VehicleId, 
                Registration = vehicleInfo!.Registration ?? "",
                CommonPool = vehicleInfo.CommonPool ?? false,
                CarpoolEnabled = vehicleInfo.IsPoolActive ?? false
            }
        };
        
        var getDriverObj = await driverRepo.GetDriverById(getCurrentBooking.UserId, request.ReturnedClientDriverId);
        var driver = new Domain.Common.Driver {
            DriverId = request.ReturnedClientDriverId,
            DriverName = getDriverObj!.FirstName,
            DriverSurname = getDriverObj.Surname,
            DriverDepartments = await driverRepo.GetDepartmentsByDriverId(getCurrentBooking.UserId, request.ReturnedClientDriverId),
            DriverQdlLicense = await driverRepo.GetQdlLicensesById(request.ReturnedClientDriverId),
            DriverPdpLicense = await driverRepo.GetPdpLicensesById(request.ReturnedClientDriverId),
            Email = getDriverObj.Email
        };
        
        var booking = new ScdfVehicleBooking {
            BookingId = request.BookingId,
            BookingStatusId = (request.ActualDropOffTime > request.DropOffTime) ? BookingStatusCode.ReturnedLate : BookingStatusCode.Returned,
            Status = getCurrentBooking!.Status,
            UpdatedDate = DateTime.UtcNow,
            VehicleId = request.VehicleId,
            EndDate = request.DropOffTime,
            Driver = driver,
            Target = vehicle,
            ReturnedIgnitionTs = request.ActualDropOffTime,
            ReturnedClientDriverId = request.ReturnedClientDriverId,
            ReturnedVehicleCommanderClientUserId = request.ReturnedVehicleCommanderClientUserId,
            ReturnedClientUserId = request.CompletedClientUserId,
            CompletedClientUserId = request.CompletedClientUserId
        };

        foreach (var r in await this.GetScdfBookingRules(request.UserId)) {
            if (r.IsEnabled == true) {
                booking.PreConditions.Add(r);
            }
        }
        
        booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowed(booking);
        
        await booking.Validate();

        return booking;
    }

    private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
        var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);
        
        List<IBookingRule> bookingRule = [];
        
        foreach (var r in userBookingRule) {
            switch (r.BookingRuleId) {
                case Constants.BookingRuleCodeCheckDriverLicenseClass:
                    bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                    bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
                case Constants.BookingRuleCodeIsDriverRequired:
                    bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
                        IsEnabled = r.Status
                    });
                    break;
            }
        }
        
        bookingRule.Add(new MustHaveVehicleRule(bookingSettings) {
            IsEnabled = true
        });
        
        bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) {
            IsEnabled = true
        });
        
        bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
            IsEnabled = true
        });
        
        return bookingRule;
    }
    
    private async Task<bool> IsVehicleCommonPoolRuleAllowed(VehicleBookingBase booking) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
        bool result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool, clientDriverDepartments, vehicleDepartments);
        return result;
    }
}