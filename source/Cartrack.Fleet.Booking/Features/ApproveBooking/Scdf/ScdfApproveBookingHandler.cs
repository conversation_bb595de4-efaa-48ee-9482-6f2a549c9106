using Cartrack.AppHost;
using Cartrack.EFCore.Models.CT;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Driver.IO.Sql;
using Cartrack.Fleet.Vehicle.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;
using Journey = Cartrack.Fleet.Booking.Domain.Common.Journey;
using Vehicle = Cartrack.Fleet.Booking.Domain.Common.Vehicle;

namespace Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;

// ReSharper disable once IdentifierTypo
public class ScdfApproveBookingHandler(
    ScdfBookingRepository repo,
    IBookingRuleRepository ruleRepo,
    ScdfVehicleBookingBuilder bookingBuilder,
    IVehicleRepository vehicleRepo,
    IDriverRepository driverRepo,
    ILogger<ScdfApproveBookingHandler> logger)
    : IRequestHandler<ScdfApproveBookingRequest, ScdfApproveBookingResponse> {
    public async Task<ScdfApproveBookingResponse> Handle(ScdfApproveBookingRequest request, CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.NotNullOrEmpty(request.RequestClientDriverId ?? "", nameof(request.RequestClientDriverId));
            Requires.IsTrue(() => request.BookingId != 0, "Booking Id is Required.");
            Requires.IsTrue(() => request.VehicleId > 0, "Vehicle is Required.");
            logger.LogInformation("[{Agency}] Approving a booking", request.Account);

            var booking = await repo.GetBooking(bookingBuilder, request.BookingId);
            if (booking is null) {
                return new ScdfApproveBookingResponse(null, new Exception($"Booking {request.BookingId} not found")) { IsServerError = false };
            }

            //1. Update the existing booking with new information from the request
            await bookingBuilder.UpdateBookingDetails(request, repo, ruleRepo);

            //2. Validate then Approve the booking
            await booking.Status.Approve();

            //3. Save the updated booking
            await repo.UpdateBooking(booking);

            //4. return the updated booking
            var updatedBooking = await repo.GetBooking(request.Account, booking.BookingId);
            return new ScdfApproveBookingResponse(updatedBooking?.ToHttpBooking());
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error approve booking");
            return new ScdfApproveBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error approve booking");
            return new ScdfApproveBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    // private async Task<ScdfVehicleBooking> ApproveFromRequest(ScdfApproveBookingRequest request) {
    //     var getCurrentBooking = await repo.GetBooking(request.Account, request.BookingId);
    //     Requires.IsTrue(() => getCurrentBooking != null, "Booking Id does not exist in the system.");
    //     
    //     List<Journey> journeys = new List<Journey>();
    //     int journeyOrder = 1;
    //     foreach (var r in request.Journeys) {
    //         journeys.Add(new Journey {
    //             LocationReference = new JourneyLocation {
    //                 Type = r.Type, Value = r.Value
    //             },
    //             Order = journeyOrder,
    //         });
    //         journeyOrder++;
    //     }
    //
    //     var vehicleInfo = await vehicleRepo.GetVehicleById(request.UserId, request.VehicleId);
    //     Requires.IsTrue(() => vehicleInfo != null, "Vehicle Id does not exist in the system.");
    //     
    //     var vehicle = new VehicleTarget {
    //         Vehicle = new Domain.Common.Vehicle {
    //             VehicleId = request.VehicleId, 
    //             Registration = vehicleInfo!.Registration ?? "",
    //             CommonPool = vehicleInfo.CommonPool ?? false,
    //             CarpoolEnabled = vehicleInfo.IsPoolActive ?? false
    //         }
    //     };
    //
    //     ClientUser clientUser = new ClientUser { 
    //         ClientUserId = request.ApproveClientUserId, 
    //         Username = "" 
    //     };
    //     var approvedBy = new List<ClientUser> { };
    //     approvedBy.Add(clientUser);
    //
    //     var getDriverObj = await driverRepo.GetDriverById(request.UserId, request.RequestClientDriverId);
    //     var driver = new Domain.Common.Driver {
    //         DriverId = request.RequestClientDriverId,
    //         DriverName = getDriverObj!.FirstName,
    //         DriverSurname = getDriverObj.Surname,
    //         DriverDepartments = await driverRepo.GetDepartmentsByDriverId(request.UserId, request.RequestClientDriverId),
    //         DriverQdlLicense = await driverRepo.GetQdlLicensesById(request.RequestClientDriverId),
    //         DriverPdpLicense = await driverRepo.GetPdpLicensesById(request.RequestClientDriverId),
    //         Email = getDriverObj.Email
    //     };
    //     
    //     var booking = new ScdfVehicleBooking {
    //         UserId = getCurrentBooking!.UserId,
    //         VehicleId = request.VehicleId,
    //         BookingId = request.BookingId,
    //         BookingStatusId = BookingStatusCode.Approved,
    //         BookingPurposeId = request.BookingPurposeId,
    //         BookingPurposeDescription = request.BookingPurposeDescription,
    //         BookingVehicleTypeId = request.BookingVehicleTypeId,
    //         Target = vehicle,
    //         Driver = driver,
    //         Status = getCurrentBooking!.Status,
    //         StartDate = DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc),
    //         EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc),
    //         //DriverType1 = request.DriverType,
    //         RequestClientDriverId = request.RequestClientDriverId,
    //         VehicleCommanderClientUserId = request.VehicleCommanderClientUserId,
    //         PickupSiteLocationId = request.PickupSiteLocationId,
    //         ReturnSiteLocationId = (request.JourneyType == JourneyType.Return ? request.PickupSiteLocationId : 0),
    //         UpdatedDate = DateTime.UtcNow,
    //         RequestedForClientUserId = request.RequestedForClientUserId,
    //         ApprovedBy = approvedBy,
    //         //RejectedBy = null,
    //         ActivationTrigger = null,
    //         Remarks = request.Remarks ?? "",
    //         NumberOfPassengers = request.NumberOfPassengers,
    //         EquipmentType1 = request.EquipmentType,
    //         Accessories = request.Accessories ?? null,
    //         JourneyType = request.JourneyType,
    //         Journeys = journeys,
    //         
    //         // Equipment Attachment 
    //         EquipmentAttachmentIds = request.EquipmentAttachmentIds, // for existing attachment
    //         EquipmentAttachments = request.EquipmentAttachments, // for new attachments
    //     };
    //
    //     foreach (var r in await this.GetScdfBookingRules(request.UserId)) {
    //         if (r.IsEnabled == true) {
    //             booking.PreConditions.Add(r);
    //         }
    //     }
    //
    //     booking.IsBookingTimeConflicting = await this.IsBookingTimeAvailable(booking);
    //     booking.IsBookingPurposeOthersRequired = await this.IsBookingPurposeOthersRequired(booking);
    //     booking.IsVehicleCommonPoolRuleAllowed = await this.IsVehicleCommonPoolRuleAllowed(booking);
    //     
    //     await booking.Validate();
    //
    //     return booking;
    // }
    //
    // private async Task<List<IBookingRule>> GetScdfBookingRules(long userId) {
    //     var (userBookingRule, bookingSettings) = await rule.GetBookingSettingRules(userId);
    //
    //     List<IBookingRule> bookingRule = [];
    //
    //     foreach (var r in userBookingRule) {
    //         switch (r.BookingRuleId) {
    //             case Constants.BookingRuleCodeCheckDriverLicenseClass:
    //                 bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) {
    //                     IsEnabled = r.Status
    //                 });
    //                 break;
    //             case Constants.BookingRuleCodeCheckDriverSpecialLicense:
    //                 bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) {
    //                     IsEnabled = r.Status
    //                 });
    //                 break;
    //             case Constants.BookingRuleCodeIsDriverRequired:
    //                 bookingRule.Add(new MustHaveDriverRule(bookingSettings) {
    //                     IsEnabled = r.Status
    //                 });
    //                 break;
    //             case Constants.BookingRuleCodeMaximumBookingTime:
    //                 bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) {
    //                     IsEnabled = r.Status
    //                 });
    //                 break;
    //             case Constants.BookingRuleCodeBookInAdvanceBy:
    //                 bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) {
    //                     IsEnabled = r.Status
    //                 });
    //                 break;
    //         }
    //     }
    //
    //     //ADD THE SCDF-specific rules
    //     bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHavePassengers(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHavePickupLocation(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHaveValidJourneys(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new MustHaveVehicleRule(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) {
    //         IsEnabled = true
    //     });
    //     return bookingRule;
    // }

    private async Task<bool> IsVehicleCommonPoolRuleAllowed(VehicleBookingBase booking) {
        var clientDriverDepartments = await driverRepo.GetDepartmentsByDriverId(booking.UserId, booking.RequestClientDriverId ?? "");
        var vehicleDepartments = await vehicleRepo.GetDepartments(booking.UserId, booking.VehicleId ?? 0);
        bool result = await repo.IsVehicleCommonPoolRuleAllowed(booking.Vehicle.CommonPool, clientDriverDepartments, vehicleDepartments);
        return result;
    }
}