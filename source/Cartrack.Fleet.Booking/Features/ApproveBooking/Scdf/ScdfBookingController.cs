﻿using Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Cartrack.Fleet.Booking.IO.Http;

// ReSharper disable once IdentifierTypo
public partial class ScdfBookingController {
    [HttpPatch]
    [Route("{bookingId:long}/approve")]
    public async Task<ActionResult<ScdfApproveBookingResponse>> ApproveBooking(long bookingId,
        [FromBody] ScdfApproveBookingRequest request) {
        request.BookingId = bookingId;
        //request.ApproveClientUserId = this.User.FindFirst(ClaimTypes.NameIdentifier)!.Value;
        //request.Account = this.User.FindFirst("Account")!.Value;

        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfApproveBookingResponse, Booking>(this.HttpContext);
    }
}