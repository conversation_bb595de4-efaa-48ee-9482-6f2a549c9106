﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Spf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.ApproveBooking.Spf;

public class SpfApproveBookingHandler(IBookingRepository repo, ILogger<SpfApproveBookingHandler> logger)
    : IRequestHandler<SpfApproveBookingRequest, SpfApproveBookingResponse> {
    public async Task<SpfApproveBookingResponse> Handle(SpfApproveBookingRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));
            logger.LogInformation("[{Agency}] Approving a booking", request.Account);

            var booking = await repo.GetBooking(request.Account, request.BookingId);
            await booking?.Status.Approve()!;
            await repo.UpdateStatus(booking.BookingId, BookingStatusCode.Approved);
            var statusHttp = new BookingStatus(booking.BookingId, Constants.Approved);
            return new SpfApproveBookingResponse(statusHttp);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error approve booking");
            return new SpfApproveBookingResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error approve booking");
            return new SpfApproveBookingResponse(null, ex) {
                IsServerError = true
            };
        }
    }

    private SpfVehicleBooking ApproveFromRequest(SpfApproveBookingRequest request) {
        throw new NotImplementedException();
    }
}