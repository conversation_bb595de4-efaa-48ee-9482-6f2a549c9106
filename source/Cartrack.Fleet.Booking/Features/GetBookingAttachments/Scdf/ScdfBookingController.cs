using Cartrack.Fleet.Booking.Features.GetBooking.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpGet]
    [Route("{id}/attachments")]
    public async Task<ActionResult<ScdfGetBookingAttachmentsResponse>> GetBookingAttachments(long id) {
        var resp = await this._mediator.Send(new ScdfGetBookingAttachmentsRequest(id));
        return resp.ToContentResult<ScdfGetBookingAttachmentsResponse, BookingAttachment[]>(this.HttpContext);
    }
}