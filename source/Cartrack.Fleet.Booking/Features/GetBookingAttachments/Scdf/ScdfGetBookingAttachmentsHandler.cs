﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf;

public class ScdfGetBookingAttachmentsHandler(ScdfBookingRepository repo, ILogger<ScdfGetBookingAttachmentsHandler> logger)
    : IRequestHandler<ScdfGetBookingAttachmentsRequest, ScdfGetBookingAttachmentsResponse> {
    public async Task<ScdfGetBookingAttachmentsResponse> Handle(ScdfGetBookingAttachmentsRequest request,
        CancellationToken cancellationToken) {
        try {
            Requires.IsTrue(() => request.Id > 0, "Booking ID must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            var bookingAttachments = await repo.GetBookingAttachments(request.Id);

            var apiBookingAttachments = bookingAttachments.Select(ba => new BookingAttachment {
                Id = ba.Id,
                BookingId = ba.BookingId,
                UrlAttachment = ba.UrlAttachment,
                FileName = ba.FileName,
                Base64Content = ba.Base64Content
            }).ToArray();

            return new ScdfGetBookingAttachmentsResponse(apiBookingAttachments);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving booking attachments");
            return new ScdfGetBookingAttachmentsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving booking attachments");
            return new ScdfGetBookingAttachmentsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}