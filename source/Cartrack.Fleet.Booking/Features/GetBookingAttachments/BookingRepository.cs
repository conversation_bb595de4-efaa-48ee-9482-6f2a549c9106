﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;
using Microsoft.EntityFrameworkCore;
using Minio.DataModel.Args;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<BookingAttachments>> GetBookingAttachments(long bookingId) {
        var attachments = await poolDbContext.BookingAttachments
            .Where(ba => ba.BookingId == bookingId)
            .ToListAsync();

        var result = new List<BookingAttachments>();

        foreach (var attachment in attachments) {
            var bookingAttachment = new BookingAttachments {
                Id = attachment.Id,
                BookingId = attachment.BookingId ?? 0,
                UrlAttachment = attachment.UrlAttachment,
                FileName = attachment.Filename
            };

            // read the file and convert to Base64
            if (!string.IsNullOrEmpty(attachment.UrlAttachment)) {
                try {
                    MinIoHandler minIo = new MinIoHandler(minioClient);

                    Uri uri = new Uri(attachment.UrlAttachment);
                    string bucketName = uri.Segments[1].TrimEnd('/');
                    string objectName = string.Join("", uri.Segments.Skip(2));

                    var getObjectArgs = new GetObjectArgs()
                        .WithBucket(bucketName)
                        .WithObject(objectName)
                        .WithCallbackStream(stream => {
                            using (var memoryStream = new MemoryStream()) {
                                stream.CopyTo(memoryStream);
                                byte[] fileBytes = memoryStream.ToArray();
                                // Convert to Base64
                                bookingAttachment.Base64Content = Convert.ToBase64String(fileBytes);
                            }
                        });

                    await minioClient.GetObjectAsync(getObjectArgs);
                }
                catch (Exception ex) {
                    bookingAttachment.Base64Content = null;
                }
            }

            result.Add(bookingAttachment);
        }

        return result;
    }
}