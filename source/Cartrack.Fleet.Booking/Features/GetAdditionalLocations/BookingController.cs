﻿using Cartrack.Fleet.Booking.Features.GetAdditionalLocations;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.Booking.IO.Http;

public partial class BookingController {
    [HttpGet]
    [Route("additionalLocations")]
    public async Task<ActionResult<GetAdditionalLocationsResponse>> GetAdditionalLocations() {
        var resp = await this._mediator.Send(new GetAdditionalLocationsRequest());
        return resp.ToContentResult<GetAdditionalLocationsResponse, AdditionalLocations>(this.HttpContext);
    }
}