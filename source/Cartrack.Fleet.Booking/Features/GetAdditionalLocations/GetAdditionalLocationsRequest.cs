﻿using MediatR;
using System.ComponentModel.DataAnnotations;

namespace Cartrack.Fleet.Booking.Features.GetAdditionalLocations;

public record GetAdditionalLocationsRequest : IRequest<GetAdditionalLocationsResponse> {
    public string? Account { get; init; } = "SCDF00001";
    public int UserId => 302739;
}

public record DateRangeFilter {
    public DateTime? StartDate { get; init; }
    public DateTime? EndDate { get; init; }
}

public record SortOptions {
    private string _column = "requestedTime";
    private string _direction = "desc";

    public string Column {
        get => _column;
        init => _column = IsValidColumn(value) ? value : "requestedTime";
    }

    public string Direction {
        get => _direction;
        init => _direction = value?.ToLower() == "asc" ? "asc" : "desc";
    }

    private bool IsValidColumn(string? column) {
        return column != null && new[] {
            "pickupTime", "requestedTime", "returnedTime"
        }.Contains(column);
    }
}