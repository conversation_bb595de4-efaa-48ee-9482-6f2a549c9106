﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Features.GetAdditionalLocations;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<List<Cartrack.Fleet.Booking.Domain.Common.AdditionalLocation>>
        GetAdditionalLocations(long userId) {
        var additionalLocations = await poolDbContext.AdditionalLocations.ToListAsync() ?? null;
        if (additionalLocations is null) {
            return new List<AdditionalLocation>();
        }

        List<AdditionalLocation> output = additionalLocations.Select(additionalLocation => new AdditionalLocation {
            Id = additionalLocation.Id,
            LocationName = additionalLocation.LocationName ?? string.Empty,
            Description = additionalLocation.Description ?? string.Empty
        }).ToList();

        return output;
    }
}