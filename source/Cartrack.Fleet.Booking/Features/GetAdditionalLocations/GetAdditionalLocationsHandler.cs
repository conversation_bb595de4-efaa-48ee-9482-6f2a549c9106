﻿using Cartrack.AppHost;
using Cartrack.Fleet.Booking.IO;
using Cartrack.Fleet.Booking.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.Features.GetAdditionalLocations;

public class
    GetAdditionalLocationsHandler(ScdfBookingRepository repo, ILogger<GetAdditionalLocationsHandler> logger) : IRequestHandler<GetAdditionalLocationsRequest, GetAdditionalLocationsResponse> {
    public async Task<GetAdditionalLocationsResponse> Handle(GetAdditionalLocationsRequest request,
        CancellationToken cancellationToken) {
        try {
            //Requires.IsTrue(() => request.Offset >= 0, "Offset must be greater than or equal to zero");
            //Requires.IsTrue(() => request.PageSize > 0, "Page size must be greater than zero");
            Requires.NotNullOrEmpty(request.Account, nameof(request.Account));

            logger.LogInformation("[{Agency}] Retrieving additional locations", request.Account);

            var result = await repo.GetAdditionalLocations(request.UserId);

            var apiAdditionalLocations = result?.ToHttpAdditionalLocations();
            return new GetAdditionalLocationsResponse(apiAdditionalLocations);
        }
        catch (RequiresException ex) {
            logger.LogError(ex, "Invalid request value. Error retrieving bookings");
            return new GetAdditionalLocationsResponse(null, ex) {
                IsServerError = false
            };
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error retrieving bookings");
            return new GetAdditionalLocationsResponse(null, ex) {
                IsServerError = true
            };
        }
    }
}