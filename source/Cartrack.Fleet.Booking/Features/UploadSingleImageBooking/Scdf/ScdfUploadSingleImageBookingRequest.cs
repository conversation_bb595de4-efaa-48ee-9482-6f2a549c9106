﻿using MediatR;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf;

public record ScdfUploadSingleImageBookingRequest : IRequest<ScdfUploadSingleImageBookingResponse> {
    [JsonIgnore] public string Account { get; init; } = "SCDF00001";
    [JsonIgnore] public long UserId { get; init; } = 302739;
    public string? ImageBase64 { get; set; }
    public string? Extension { get; set; }
}