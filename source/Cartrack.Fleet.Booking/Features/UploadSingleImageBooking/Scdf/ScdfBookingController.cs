﻿using Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf;
using Cartrack.Fleet.Common;
using Microsoft.AspNetCore.Mvc;

namespace Cartrack.Fleet.Booking.IO.Http;

public partial class ScdfBookingController {
    [HttpPost]
    [Route("uploadSingleImageTemp")]
    public async Task<ActionResult<ScdfUploadSingleImageBookingResponse>> UploadSingleImageBooking(
        [FromBody] ScdfUploadSingleImageBookingRequest request) {
        var resp = await this._mediator.Send(request);
        return resp.ToContentResult<ScdfUploadSingleImageBookingResponse, TempUploadImageStatus>(this.HttpContext);
    }
}