﻿using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Common;


namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class BookingRepository {
    public async Task<BookingAttachments> TempUploadSingleImage(string? base64Input, string? extension) {
        string uuid = Guid.NewGuid().ToString();
        string fileName = uuid;
        string minIoImageUrl = "";

        var output = new BookingAttachments();
        if (base64Input is null) {
            return output;
        }

        MinIoHandler minIo = new MinIoHandler(minioClient);
        MinIoResponseResult result = await minIo.TempUploadBase64ImageAsync(base64Input, fileName, extension ?? "");

        output = new BookingAttachments {
            TempFileAttachmentGuid = result.Guid,
            ContentType = result.ContentType,
            Extension = result.Extension,
            Status = string.IsNullOrEmpty(result.ErrorMessage) ? "OK" : "NOT_OK",
            ErrorMessage = result.ErrorMessage,
        };
        return output;
    }
}