﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Booking.IO.Http;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Cartrack.Fleet.Booking;

public static class BookingStartup {
    public static void Register(IServiceCollection builderServices, AppSettings appSetting) {
        //builderServices.AddScoped<IAuthRepository, AuthRepository>();

        builderServices.AddMediatR(cfg => {
            cfg.RegisterServicesFromAssemblyContaining<BookingController>();
            cfg.RegisterServicesFromAssemblyContaining<SpfBookingController>();
            cfg.RegisterServicesFromAssemblyContaining<ScdfBookingController>();
        });

        builderServices.AddControllers()
            .AddApplicationPart(typeof(BookingController).Assembly)
            .AddApplicationPart(typeof(SpfBookingController).Assembly)
            .AddApplicationPart(typeof(ScdfBookingController).Assembly);
        
        builderServices.AddScoped<ScdfBookingRepository>();
        builderServices.AddScoped<SpfBookingRepository>();
        builderServices.AddScoped<IBookingRepository, BookingRepository>();
        builderServices.AddScoped<IBookingRuleRepository, BookingRuleRepository>();
        builderServices.AddScoped<IBookingFilterService, BookingFilterService>();
        
        builderServices.AddScoped<ScdfVehicleBookingBuilder>();
        builderServices.AddScoped<Func<string, IVehicleBookingBuilder>>(serviceProvider => key => {
            if (key == "SCDF00001") {
                return serviceProvider.GetService<ScdfVehicleBookingBuilder>()!;
            }

            throw new NotImplementedException($"Agency {key} not supported");
        });
    }
}