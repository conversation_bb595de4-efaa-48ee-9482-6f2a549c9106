﻿using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Db = Cartrack.EFCore.Models.Pool;
using Api = Cartrack.Fleet.Booking.IO.Http;

namespace Cartrack.Fleet.Booking.IO;

public static class BookingSqlExtensions {
    public static VehicleBookingBase ToBooking(this Db.Booking dbBooking) {
        throw new NotImplementedException("TODO");
    }

    public static Api.AdditionalLocations ToHttpAdditionalLocations(this List<AdditionalLocation> entities) {
        if (entities == null)
            return new Api.AdditionalLocations {
                Data = []
            };

        return new Api.AdditionalLocations {
            Data = entities
        };
    }

    public static Api.RequestPurposes ToHttpRequestPurposes(this List<RequestPurpose>? entities) {
        return entities == null ? new Api.RequestPurposes { Data = [] } : new Api.RequestPurposes { Data = entities };
    }
    
    public static Api.RejectBookingReasons ToHttpRejectBookingReasons(this List<RejectBookingReason>? entities) {
        return entities == null ? new Api.RejectBookingReasons { Data = [] } : new Api.RejectBookingReasons { Data = entities };
    }
    
    public static Api.CancelBookingReasons ToHttpCancelBookingReasons(this List<CancelBookingReason>? entities) {
        return entities == null ? new Api.CancelBookingReasons { Data = [] } : new Api.CancelBookingReasons { Data = entities };
    }
    
    public static Api.ForceTerminateBookingReasons ToHttpForceTerminateBookingReasons(this List<ForceTerminateBookingReason>? entities) {
        return entities == null ? new Api.ForceTerminateBookingReasons { Data = [] } : new Api.ForceTerminateBookingReasons { Data = entities };
    }
    
    public static Api.RequestPurposeVehicleCategoryMaps ToHttpRequestPurposeVehicleCategoriesMap(this List<RequestPurposeVehicleCategoryMap>? entities, int total) {
        return entities == null ? new Api.RequestPurposeVehicleCategoryMaps { Total = 0, Data = [] } : new Api.RequestPurposeVehicleCategoryMaps { Total = total, Data = entities };
    }
    
    
    public static Api.Booking ToHttpBooking(this VehicleBookingBase entity) {
        var vehicleTarget = entity.Target as VehicleTarget;
        return new Api.Booking {
            Id = entity.BookingId,
            StatusId = entity.Status.Id,
            Status = entity.Status.TranslationKey,
            StartDate = entity.StartDate,
            EndDate = entity.EndDate,
            PickupTime = entity.PickupTime,
            DropoffTime = entity.DropoffTime,
            Description = entity.Description,
            Purpose = entity.Purpose?.Title,
            VehicleId = vehicleTarget?.Vehicle?.VehicleId ?? 0,
            VehicleRegistration = vehicleTarget?.Vehicle?.Registration,
            //CreatedDate = entity.CreatedDate,
            //UpdatedDate = entity.UpdatedDate,
            Type = entity.Type.ToString(),
            RequestClientUserId = entity.RequestedBy?.ClientUserId,
            RequestedBy = entity.RequestedBy?.Username,
            RequestedDate = entity.RequestedDate,

            // commented because in this branch does not have GetBooking yet.
            Journeys = entity.Journeys.Select(j => new Http.Journey {
                Id = j.Id,
                StartTime = j.Start,
                EndTime = j.End,
                Location = j.Location,
                Order = j.Order,
                JourneyLocationType = j.LocationReference.Type,
                JourneyLocationId = j.LocationReference.Value
            }).ToArray(),
            Accessories = entity.DetailedAccessories
                .Select(a =>
                    new Api.BookingAccessory {
                        Id = a.Id,
                        TypeId = a.TypeId,
                        Name = a.Name,
                        Description = a.Description
                    })
                .ToArray(),
            RequestClientDriverId = entity.Driver?.DriverId,
            DriverName = entity.Driver?.FullName,
            DriverEmail = entity.Driver?.Email,
            BookingPurposeId = entity.Purpose?.Id,
            BookingPurposeTitle = entity.Purpose?.Title,
            BookingPurposeDescription = entity.Purpose?.Description,
            VehicleCategoryId = entity.VehicleCategory?.Id,
            VehicleCategoryName = entity.VehicleCategory?.Name,
            CommanderClientUserId = entity.VehicleCommander?.ClientUserId,
            CommanderUsername = entity.VehicleCommander?.Username,
            PickupLocationId = entity.PickupLocation.Id,
            PickupLocationName = entity.PickupLocation.Name,
            RequestedForClientUserId = entity.RequestedForClientUserId,
            LocationType = entity.LocationType,
            DriverType = entity.DriverType,
            VehicleCommanderType = entity.VehicleCommanderType,
            EquipmentType = entity.EquipmentType,
            NumberOfPassengers = entity.NumberOfPassengers,

            //BookingReference = entity.BookingReference,
            KeyReturnTs = entity.KeyReturnTs,
            KeyCollectionTs = entity.KeyCollectionTs,
            ApprovedManagers = entity.ApprovedBy
                .Select(cu => new Api.DisplayUser {
                    ClientUserId = cu.ClientUserId, Username = cu.Username ?? string.Empty
                })
                .ToArray(),
            RejectedManagers = entity.RejectedBy
                .Select(cu => new Api.DisplayUser {
                    ClientUserId = cu.ClientUserId, Username = cu.Username ?? string.Empty
                })
                .ToArray(),
            Remarks = entity.Remarks,
        };
    }

    public static Db.Booking FromBooking(this VehicleBookingBase booking) {
        throw new NotImplementedException("TODO");
    }
}