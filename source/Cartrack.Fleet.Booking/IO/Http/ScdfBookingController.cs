﻿using Cartrack.Fleet.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Booking.IO.Http;

[ApiController]
[Route("scdf/booking")]
public partial class ScdfBookingController : ControllerBase {
    private readonly AppSettings _appSettings;
    private readonly ILogger<ScdfBookingController> _logger;
    private readonly IMediator _mediator;

    public ScdfBookingController(ILogger<ScdfBookingController> logger, IMediator mediator, AppSettings appSettings) {
        this._logger = logger;
        this._mediator = mediator;
        this._appSettings = appSettings;
    }
}