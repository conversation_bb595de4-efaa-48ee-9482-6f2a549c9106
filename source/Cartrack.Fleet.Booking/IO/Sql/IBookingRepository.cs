using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Driver.Domain.Common;
using Cartrack.Fleet.Vehicle.Domain.Common;

namespace Cartrack.Fleet.Booking.IO.Sql;

public interface IBookingRepository {
    Task<VehicleBookingBase?> GetBooking(string agency, long id);
    Task<VehicleBookingBase?> GetBooking(IVehicleBookingBuilder builder, long id);

    Task<BookingsResult> GetBookings(
        string agency,
        ServerRequestModel serverRequest
    );

    Task<long> CreateBooking(VehicleBookingBase booking);
    Task<long> UpdateBooking(VehicleBookingBase booking);
    Task<long> ApproveBooking(VehicleBookingBase booking);
    Task<long> RejectBooking(VehicleBookingBase booking);
    Task<long> ActivateBooking(VehicleBookingBase booking);
    Task ForceTerminateBooking(List<long> bookingIds, string forceTerminateClientUserId, int forceTerminateReasonId, string bookingForceTerminateNotes);
    Task<long> EndBookingScdf(VehicleBookingBase booking);
    Task UpdateStatus(long bookingId, BookingStatusCode bookingStatusId);
    Task<List<BookingAccessory>?> GetBookingAccessory(long bookingId);
    Task<List<AdditionalLocation>> GetAdditionalLocations(long userId);
    Task<List<RequestPurpose>> GetRequestPurposes(long userId, long bookingPurposeId);
    Task<List<RejectBookingReason>> GetRejectBookingReasons(long userId, long rejectBookingReasonId);
    Task<List<CancelBookingReason>> GetCancelBookingReasons(long userId, long cancelBookingReasonId);
    Task<List<ForceTerminateBookingReason>> GetForceTerminateBookingReasons(long userId, long forceTerminateBookingReasonId);
    Task<List<RequestPurposeVehicleCategoryMap>> GetRequestPurposeVehicleCategoriesMaps(long userId, long bookingPurposeId, int page, int pageSize);
    Task<int> GetRequestPurposeVehicleCategoriesMapsTotal(int userId);
    Task AddOrUpdateBookingJourneys(long userId, long bookingId, IList<Journey> bookingJourney);
    Task AddOrUpdateBookingAccessory(long bookingId, List<int>? bookingAccessory);
    Task<bool> IsBookingPurposeOthersRequired(VehicleBookingBase booking);
    Task<bool> IsBookingTimeConflicting(VehicleBookingBase booking);
    Task<bool> IsVehicleCommonPoolRuleAllowed(bool commonPool, List<DriverDepartment> clientDriverDepartments, List<VehicleDepartment> vehicleDepartments);
    Task<ClientUser> GetClientUserById(string? clientUserId);
    Task<BookingAttachments> TempUploadSingleImage(string? base64Input, string? extension);
    Task<BookingAttachments> DeleteTempUploadSingleImage(string? guid, string? extension);
    Task<List<BookingAttachments>> GetBookingAttachments(long bookingId);
    Task<BookingCancelReasonBase?> GetBookingCancelReason(string agency, long id);
    Task CancelBooking(long bookingId, string canceledClientUserId, string bookingCancelNotes);
    Task<Domain.Common.Driver> GetClientDriverById(string id);
    Task UpdateBookingApproval(VehicleBookingBase booking);
}