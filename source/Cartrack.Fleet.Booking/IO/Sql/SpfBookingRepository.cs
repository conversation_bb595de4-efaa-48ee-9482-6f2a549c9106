﻿using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Booking.Features.GetBookings.Scdf;
using Cartrack.Fleet.Common.IO.Sql;
using Minio;

namespace Cartrack.Fleet.Booking.IO.Sql;

public partial class SpfBookingRepository(AppTfmsCustomDbContext tfmsCustomDbContext, AppFleetDbContext fleetDbContext, AppCtDbContext ctDbContext, AppPoolDbContext poolDbContext, IMinioClient minioClient, IBookingFilterService filterService, Func<string, IVehicleBookingBuilder> bookingBuilder) : BookingRepository(tfmsCustomDbContext, fleetDbContext, ctDbContext, poolDbContext, minioClient, filterService, bookingBuilder) {
    
}