
CREATE TABLE IF NOT EXISTS pool.booking_journey_type (
    id serial4 primary key,
    user_id INT,
    variable_code varchar(255) not NULL,
    name VA<PERSON><PERSON><PERSON>(255),
    description TEXT,
    is_deleted BOOLEAN default false not NULL
);

CREATE TABLE IF NOT EXISTS pool.accessory_type (
    id serial4 primary key,
    user_id INT,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    description TEXT,
    is_deleted <PERSON><PERSON><PERSON><PERSON><PERSON> default false not NULL
);

CREATE TABLE IF NOT EXISTS pool.accessory (
    id serial4 primary key,
    user_id INT,
    accessory_type_id INT,
    name VARCHAR(255),
    description TEXT,
    is_deleted BOOLEAN default false not NULL,
    FOREIGN KEY (accessory_type_id) REFERENCES pool.accessory_type(id) ON DELETE CASCADE ON UPDATE CASCADE;
);

CREATE TABLE IF NOT EXISTS pool.booking_accessories (
    id serial4 PRIMARY KEY,
    booking_id BIGINT,
    accessory_id INT,
    is_deleted BOOLEAN default false not null,
    FOR<PERSON><PERSON><PERSON> KEY (accessory_id) REFERENCES pool.accessory(id) ON DELETE CASCADE ON UPDATE CASCADE;
);

CREATE TABLE IF NOT EXISTS pool.booking_journey (
    id serial4 PRIMARY KEY,
    user_id INT,
    booking_id BIGINT,
    booking_journey_type_varcode VARCHAR(255),
    start_location VARCHAR,
    end_location VARCHAR,
    start_ts timestamptz NOT NULL,
    end_ts timestamptz NOT NULL
);


CREATE TABLE IF NOT EXISTS pool.booking_silent_hours (
    id serial4 PRIMARY KEY,
    user_id INT,
    start_ts timestamptz,
    end_ts timestamptz,
    every_mon BOOLEAN default FALSE NOT NULL,
    every_tue BOOLEAN default FALSE NOT NULL,
    every_wed BOOLEAN default FALSE NOT NULL,
    every_thu BOOLEAN default FALSE NOT NULL,
    every_fri BOOLEAN default FALSE NOT NULL,
    every_sat BOOLEAN default FALSE NOT NULL,
    every_sun BOOLEAN default FALSE NOT NULL,
    non_working_day timestamptz
);

CREATE TABLE IF NOT EXISTS pool.booking_attachments (
    id serial4 PRIMARY KEY,
    booking_id BIGINT,
    url_attachment TEXT,
    content_type VARCHAR
);

CREATE TABLE IF NOT EXISTS pool.additional_locations (
    id serial8 PRIMARY KEY,
    location_name VARCHAR,
    description TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_ts timestamptz DEFAULT NOW() NOT NULL,
    updated_ts timestamptz DEFAULT NOW() NOT NULL
);


CREATE TABLE IF NOT EXISTS tfms_custom.scdf_booking_additional_info (
    id serial4 PRIMARY KEY,
    booking_id BIGINT,
    passenger_count INT,
    vehicle_commander_client_user_id fleet.ct_uuid,
    is_self_drive BOOLEAN
);


ALTER TABLE pool.booking ADD COLUMN remarks VARCHAR;
ALTER TABLE pool.au$booking ADD COLUMN remarks VARCHAR;

ALTER TABLE pool.booking_journey ADD journey_location_type varchar NULL;
ALTER TABLE pool.booking_journey ADD journey_location_id varchar NULL;
COMMENT ON COLUMN pool.booking_journey.journey_location_id IS 'depends on journey_location_type. poi --> poi_id, location --> site_location_id. freetext --> save into poi table';

ALTER TABLE pool.booking_attachments ADD filename varchar NULL;
COMMENT ON COLUMN pool.booking_attachments.filename IS 'only filename text';
