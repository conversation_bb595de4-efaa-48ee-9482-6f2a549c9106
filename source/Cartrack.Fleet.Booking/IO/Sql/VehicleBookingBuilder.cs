﻿using Cartrack.AppHost;
using Cartrack.EFCore.Models.Pool;
using Cartrack.Fleet.Booking.Domain;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.States;
using Cartrack.Fleet.Booking.Domain.Scdf;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.EntityFrameworkCore;
//using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;

//using VehicleBooking = Cartrack.Fleet.Booking.Domain.VehicleBooking;

namespace Cartrack.Fleet.Booking.IO.Sql;

public abstract class VehicleBookingBuilder : IVehicleBookingBuilder {
    protected record ClientUserRecord(string ClientUserId, string UserName);

    protected readonly AppCtDbContext _ctDbContext;
    protected readonly AppFleetDbContext _fleetDbContext;
    protected readonly AppPoolDbContext _poolDbContext;
    protected readonly AppTfmsCustomDbContext _tfmsCustomDbContext;
    protected EFCore.Models.Pool.Booking _booking;
    protected List<ClientUserRecord> _clientUsers = [];
   
    private List<EFCore.Models.Pool.BookingApproval> _approvals = [];

    protected VehicleBookingBuilder(AppCtDbContext ctDbContext, AppFleetDbContext fleetDbContext, AppPoolDbContext poolDbContext, AppTfmsCustomDbContext tfmsCustomDbContext) {
        this._ctDbContext = ctDbContext;
        this._fleetDbContext = fleetDbContext;
        this._poolDbContext = poolDbContext;
        this._tfmsCustomDbContext = tfmsCustomDbContext;
    }

    public abstract Task<VehicleBookingBase?> Build(long id); 
    public VehicleBookingBase? VehicleBooking { get; private set; }

    public abstract VehicleBookingBase Create();
    public virtual async Task Start(long bookingId) {
        var booking = await _poolDbContext.Bookings.FirstOrDefaultAsync(b => b.BookingId == bookingId);
        Requires.NotNull(booking, nameof(booking)); 
        
        this._booking = booking!;
        var vehicleBooking = this.Create();
        vehicleBooking.UserId = this._booking.UserId;
        vehicleBooking.BookingId = this._booking.BookingId;
        vehicleBooking.StartDate = this._booking.StartTs;
        vehicleBooking.EndDate = this._booking.EndTs;
        vehicleBooking.Description = this._booking.BookingPurposeDescription;
        vehicleBooking.CreatedDate = this._booking.RequestTs;
        vehicleBooking.UpdatedDate = this._booking.DecisionTs ?? this._booking.RequestTs;
        vehicleBooking.Type = this._booking.BookingType.HasValue ? (BookingType)this._booking.BookingType.Value : BookingType.Standard;
        vehicleBooking.RequestedDate = this._booking.RequestTs;
        vehicleBooking.BookingReference = this._booking.BookingReference;
        vehicleBooking.KeyReturnTs = this._booking.KeyReturnTs;
        vehicleBooking.PickupTime = this._booking.PickupIgnitionTs;
        vehicleBooking.DropoffTime = this._booking.ReturnedIgnitionTs;
        vehicleBooking.Remarks = this._booking.Remarks;
        vehicleBooking.Status = BaseBookingState.GetState(this._booking.BookingStatusId, vehicleBooking);
        this.VehicleBooking = vehicleBooking;
        this._clientUsers = await this.GetClientUserIds();
       
        // Get booking approvals
        this._approvals = await this._poolDbContext.BookingApprovals
            .Where(ba => ba.BookingId == this._booking.BookingId)
            .ToListAsync();
    }

    public async Task IncludePurpose() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
            
        if (this._booking.BookingPurposeId.HasValue && this._booking.BookingPurposeId.Value > 0) {
            await this.IncludePurpose(this._booking.BookingPurposeId.Value);
        }
    }

    public async Task IncludePurpose(long bookingPurposeId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => bookingPurposeId > 0, () => "Booking purpose id must be greater than zero.");
        
        var purpose = await this._poolDbContext.BookingPurposes
            .Where(bp => bp.BookingPurposeId == bookingPurposeId)
            .Select(bp => bp.BookingPurpose1)
            .FirstOrDefaultAsync();

        this.VehicleBooking!.Purpose = new RequestPurpose {
            Id = bookingPurposeId,
            Title = purpose ?? string.Empty,
            Description = this._booking.BookingPurposeDescription
        };
    }

    public async Task IncludePickupLocation() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking.PickupSiteLocationId > 0)
            await this.IncludePickupLocation(this._booking.PickupSiteLocationId);
    }


    public async Task IncludePickupLocation(long pickupSiteLocationId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => pickupSiteLocationId > 0, () => "Pickup site location id must be greater than zero.");
     
        // Get pickup location
        var pickupLocation = await this._fleetDbContext.SiteLocations
            .Where(sl => sl.SiteLocationId == pickupSiteLocationId)
            .Select(sl => new { sl.SiteLocationId, sl.SiteLocationName })
            .FirstOrDefaultAsync();

        this.VehicleBooking!.PickupLocation = new RequestLocation { Id = pickupSiteLocationId, Name = pickupLocation?.SiteLocationName ?? string.Empty };
    }

    public async Task IncludeVehicle() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking.VehicleId.HasValue && this._booking.VehicleId.Value > 0) {
            await this.IncludeVehicle(this._booking.VehicleId.Value);
        }
    }

    public async Task IncludeVehicle(long vehicleId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.IsTrue(() => vehicleId > 0, () => "VehicleId must be greater than zero");
        
        var vehicle = await this._ctDbContext.Vehicles
            .Where(v => v.VehicleId == vehicleId)
            .Select(v => new { v.VehicleId, v.Registration })
            .FirstOrDefaultAsync();

        if (vehicle != null) {
                this.VehicleBooking!.Target = new VehicleTarget { Vehicle = new Domain.Common.Vehicle { VehicleId = vehicle.VehicleId, Registration = vehicle.Registration } };
        }

    }

    public async Task IncludeCategory() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        if (this._booking.BookingVehicleTypeId.HasValue) {
            var vehicleType = await this._poolDbContext.BookingVehicleTypes
                .Where(vt => vt.BookingVehicleTypeId == this._booking.BookingVehicleTypeId.Value)
                .Select(vt => vt.BookingVehicleType1)
                .FirstOrDefaultAsync();

            this.VehicleBooking!.VehicleCategory = new RequestVehicleType {
                Id = this._booking.BookingVehicleTypeId.Value, Name = vehicleType ?? string.Empty
            };
        }
    }

    public async Task IncludeDriver() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get driver details
        if (!string.IsNullOrEmpty(this._booking.RequestClientDriverId)) {
            await this.IncludeDriver(this._booking.RequestClientDriverId);
        }
    }

    public async Task IncludeDriver(string requestClientDriverId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.NotNullOrEmpty(requestClientDriverId, nameof(requestClientDriverId));

        // Get driver details
        var driver = await this._fleetDbContext.ClientDrivers
            .Where(d => d.ClientDriverId == requestClientDriverId)
            .Select(d => new { d.DriverName, DriverSurname = d.DriverSurname ?? string.Empty })
            .FirstOrDefaultAsync();

        if (driver != null) {
            this.VehicleBooking!.Driver = new Domain.Common.Driver {
                DriverId = requestClientDriverId, DriverName = driver.DriverName, DriverSurname = driver.DriverSurname
            };
        }

    }

    public async Task IncludeJourneys() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get journeys for this booking
        var journeys = await this._poolDbContext.BookingJourneys
            .Where(j => j.BookingId == this._booking.BookingId)
            .Select(j => new Journey {
                Id = j.Id,
                Start = j.StartTs,
                End = j.EndTs,
                Location = j.Location ?? string.Empty,
                Order = j.Order,
                LocationReference = new JourneyLocation() { Type = j.JourneyLocationType ?? string.Empty, Value = j.JourneyLocationId ?? string.Empty, }
            })
            .ToListAsync();

        await this.IncludeJourneys(journeys);
    }

    public Task IncludeJourneys(IList<Journey> journeys) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        this.VehicleBooking!.Journeys = journeys;
        return Task.CompletedTask;
    }

    public async Task IncludeAccessories() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get accessories for this booking
        var bookingAccessories = await this._poolDbContext.BookingAccessories
            .Where(ba => ba.BookingId == this._booking.BookingId && !ba.IsDeleted)
            .Join(
                this._poolDbContext.Accessories,
                ba => ba.AccessoryId,
                a => a.Id,
                (ba, a) => new {
                    BookingAccessory = ba, Accessory = a
                }
            )
            .ToListAsync();

        foreach (var item in bookingAccessories) {
            this.VehicleBooking!.DetailedAccessories.Add(new BookingAccessoryDetail {
                Id = item.Accessory.Id,
                TypeId = item.Accessory.AccessoryTypeId,
                Name = item.Accessory.Name ?? string.Empty,
                Description = item.Accessory.Description ?? string.Empty,
            });
        }
    }

    private async Task<List<ClientUserRecord>> GetClientUserIds() {
           
        // Create a dictionary for client usernames that we'll need
        var clientUserIds = new HashSet<string>();

        if (!string.IsNullOrEmpty(this._booking.RequestClientUserId))
            clientUserIds.Add(this._booking.RequestClientUserId);

        if (!string.IsNullOrEmpty(this._booking.DecisionClientUserId))
            clientUserIds.Add(this._booking.DecisionClientUserId);

        foreach (var approval in this._approvals) {
            if (!string.IsNullOrEmpty(approval.UnitManagerId))
                clientUserIds.Add(approval.UnitManagerId);
        }

        var clientUsers2 = await this._fleetDbContext.ClientUsers
            .Where(cu => clientUserIds.Contains(cu.ClientUserId))
            .Select(cu => new ClientUserRecord(cu.ClientUserId, cu.UserName))
            .ToListAsync();

        return clientUsers2;
    }

    

    public Task IncludeRequestedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set requested by user
        // Database auto add empty space of RequestClientUserId so we will need to trim it
        if (!string.IsNullOrEmpty(this._booking.RequestClientUserId) &&
            !string.IsNullOrEmpty(this._booking.RequestClientUserId.Trim())) {
            this.VehicleBooking!.RequestedBy = new ClientUser {
                ClientUserId = this._booking.RequestClientUserId,
                Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == this._booking.RequestClientUserId)?.UserName ?? this._booking.Requestor ?? ""
            };
        }
        else {
            this.VehicleBooking!.RequestedBy = new ClientUser {
                ClientUserId = string.Empty, Username = this._booking.Requestor ?? "",
            };
        }

        return Task.CompletedTask;
    }

    public Task IncludeApprovedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set approved managers
        var approvedManagers = new List<ClientUser>();

        var approvedManagerIds = this._approvals
            .Where(ba => ba.IsApproved == true)
            .Select(ba => ba.UnitManagerId)
            .ToList();

        foreach (var managerId in approvedManagerIds) {
            approvedManagers.Add(new ClientUser {
                ClientUserId = managerId,
                Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == managerId)?.UserName ?? ""
            });
        }

        // If no approvals found but booking is approved, consider decision_client_user_id
        if (!approvedManagers.Any() && this._booking.IsApproved == true) {
            if (!string.IsNullOrEmpty(this._booking.DecisionClientUserId)) {
                approvedManagers.Add(new ClientUser {
                    ClientUserId = this._booking.DecisionClientUserId,
                    Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == this._booking.DecisionClientUserId)?.UserName ?? ""
                });
            }
            else {
                // System approval case
                approvedManagers.Add(new ClientUser { ClientUserId = "", Username = "System" });
            }
        }

        this.VehicleBooking!.ApprovedBy = approvedManagers;
        return Task.CompletedTask;
    }

    public Task IncludeRejectedBy() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set rejected managers
        var rejectedManagers = new List<ClientUser>();

        var declinedManagerIds = this._approvals
            .Where(ba => ba.IsApproved == false)
            .Select(ba => ba.UnitManagerId)
            .ToList();

        foreach (var managerId in declinedManagerIds) {
            rejectedManagers.Add(new ClientUser {
                ClientUserId = managerId, 
                Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == managerId)?.UserName ?? ""
            });
        }

        // If no declined approvals found but booking is declined, consider decision_client_user_id
        if (!rejectedManagers.Any() && (this._booking.IsApproved == false || this._booking.BookingStatusId == 4)) {
            if (!string.IsNullOrEmpty(this._booking.DecisionClientUserId)) {
                rejectedManagers.Add(new ClientUser {
                    ClientUserId = this._booking.DecisionClientUserId,
                    Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == this._booking.DecisionClientUserId)?.UserName ?? ""
                });
            }
            else {
                // System decline case
                rejectedManagers.Add(new ClientUser {
                    ClientUserId = "", Username = "System"
                });
            }
        }

        this.VehicleBooking!.RejectedBy = rejectedManagers;
        return Task.CompletedTask;
    }
}