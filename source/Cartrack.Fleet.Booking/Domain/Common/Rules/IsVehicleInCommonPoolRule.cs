﻿namespace Cartrack.Fleet.Booking.Domain.Common.Rules;

public class IsVehicleInCommonPoolRule(BookingSettings settings) : IBookingRule {
    public string Name => nameof(IsVehicleInCommonPoolRule);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = "You are only able to book vehicle within its department with driver's.";

    public (bool, string) Execute(IBooking booking) {
        bool pass = booking.IsVehicleCommonPoolRuleAllowed;
        return (pass, pass ? string.Empty : $"Validation failed. {Description}");
    }
}