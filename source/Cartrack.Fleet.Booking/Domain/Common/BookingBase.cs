﻿using Cartrack.Fleet.Booking.Domain.Common.CancellationTriggers;
using Cartrack.Fleet.Booking.Domain.Common.States;

namespace Cartrack.Fleet.Booking.Domain.Common;

public abstract class BookingBase : IBooking {
    public long UserId { get; set; }
    public IBookingCancellationTrigger CancellationTrigger { get; set; } = new UnclaimedVehicleTrigger();
    public long BookingId { get; set; }
    public BookingStatusCode BookingStatusId { get; set; }
    public long? BookingVehicleTypeId { get; set; }
    public IBookingState Status { get; set; } = new UnknownState(null!);
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime? PickupTime { get; set; }
    public DateTime? DropoffTime { get; set; }
    public string? Description { get; set; }
    public string? RequestClientDriverId { get; set; }
    public string? RequestClientUserId { get; set; }
    public string? Requestor { get; set; }
    public RequestPurpose Purpose { get; set; } = RequestPurpose.Unknown();
    public long BookingPurposeId { get; set; }
    public string? BookingPurposeDescription { get; set; }
    public long? PickupSiteLocationId { get; set; }
    public long? ReturnSiteLocationId { get; set; }
    public Driver? Driver { get; set; }
    public RequestLocation PickupLocation { get; set; }
    public RequestVehicleType VehicleCategory { get; set; }
    public IBookingTarget? Target { get; set; }
    public IList<IBookingAccessory> Accessories { get; } = [];
    public DateTime? KeyCollectionTs { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public BookingType Type { get; set; } = BookingType.Standard;
    public ClientUser RequestedBy { get; set; } = ClientUser.Empty();
    public ClientUser RequestedFor { get; set; } = ClientUser.Empty();
    public Fleet.Driver.Domain.Common.Driver? RequestedClientDriverId { get; set; }
    public ClientUser? RequestedClientUserId { get; set; }
    public DateTime RequestedDate { get; set; }
    public IList<ClientUser> ApprovedBy { get; set; } = [];
    public IList<ClientUser> RejectedBy { get; set; } = [];
    public IList<IBookingRule> PreConditions { get; } = [];
    public IBookingActivationTrigger ActivationTrigger { get; set; } = new ManualActivationTrigger();
    public string? BookingReference { get; set; }
    public bool IsAutoApproveEnabled { get; set; }
    public DateTime? KeyReturnTs { get; set; }
    public BookingType BookingType { get; set; } = BookingType.Standard;
    public string? Remarks { get; set; }
    public abstract long? VehicleId { get; set; }
    public abstract bool IsVehicleCommonPoolRuleAllowed{get;set;}
    public long BookingRejectReasonId { get; init; }
    public string BookingRejectReason { get; init; } = string.Empty;
    public abstract Task Validate();
}

public abstract class VehicleBookingBase : BookingBase {
    public override long? VehicleId { get; set; }
    public JourneyType JourneyType { get; set; }
    public abstract IList<Journey> Journeys { get; set; }
    public ClientUser? VehicleCommander { get; set; }
    public string? Remarks { get; set; }
    public string? RequestClientDriverId { get; set; }
    public string? ReturnedClientDriverId { get; set; }
    public string? ReturnedClientUserId{get;set;}
    public string? RequestedForClientUserId { get; set; }
    public string? ActivatedClientUserId { get; set; }
    public string? CompletedClientUserId { get; set; }
    public int? LocationType { get; set; }
    public int? DriverType { get; set; }
    public int? VehicleCommanderType { get; set; }
    public int? EquipmentType { get; set; }
    public List<int>? Accessories { get; set; }
    public List<BookingAccessoryDetail> DetailedAccessories { get; set; } = [];
    public int? NumberOfPassengers { get; set; }
    public VehicleCommanderType? VehicleCommanderType1 { get; set; }
    public string? VehicleCommanderClientUserId { get; set; }
    public string? ReturnedVehicleCommanderClientUserId { get; set; }
    public List<int>? EquipmentAttachmentIds { get; set; } // for existing attachment in a booking (for edit purpose)
    public List<InputEquipmentAttachment>? EquipmentAttachments { get; set; } // for (non-existent) new attachments (for create / edit purpose)
    public bool IsBookingTimeConflicting { get; set; }
    public override bool IsVehicleCommonPoolRuleAllowed {get;set;}
    public DriverType? DriverType1 { get; set; }
    public EquipmentType? EquipmentType1 { get; set; }
    public Vehicle Vehicle => ((VehicleTarget)this.Target).Vehicle;
    public DateTime? KeyReturnTs { get; set; }
    public DateTime? PickedupIgnitionTs { get; set; }
    public DateTime? ReturnedIgnitionTs { get; set; }
}

public class BookingsResult {
    public VehicleBookingBase[] Bookings { get; set; } = [];
    public int Total { get; set; }
    public Dictionary<long, int> StatusCounts { get; set; } = new();
}

public record InputEquipmentAttachment(string Guid, string Extension, string RealFilename);