﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ActiveLateState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ActiveLate;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.ActivateLate;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyActivateLate;

    public override Task ReturnLate() {
        this.Booking.Status = new ReturnedLateState(this.Booking);
        return Task.CompletedTask;
    }

    public override Task Terminate() {
        this.Booking.Status = new ForceTerminatedState(this.Booking);
        return Task.CompletedTask;
    }
}