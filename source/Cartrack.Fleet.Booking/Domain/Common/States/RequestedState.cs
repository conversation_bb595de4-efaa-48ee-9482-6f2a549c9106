﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class RequestedState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.Requested;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Requested;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyRequested;

    public override Task Request() {
        return Task.CompletedTask;
    }

    public override Task Approve() {
        
        var prevState = this.Booking.Status;
        try {
            this.Booking.Validate();
            this.Booking.Status = new ApprovedState(this.Booking);
            this.Booking!.BookingStatusId = (BookingStatusCode)this.Booking.Status.Id;
        }
        catch {
            this.Booking.Status = prevState;
            throw;
        }

        return Task.CompletedTask;
    }

    public override Task Reject() {
        this.Booking.Status = new RejectedState(this.Booking);
        return Task.CompletedTask;
    }

    public override Task Cancel() {
        this.Booking.Status = new CancelledState(this.Booking);
        return Task.CompletedTask;
    }
}