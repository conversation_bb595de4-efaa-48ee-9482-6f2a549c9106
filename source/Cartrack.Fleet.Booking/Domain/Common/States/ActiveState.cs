﻿namespace Cartrack.Fleet.Booking.Domain.Common.States;

public class ActiveState(IBooking booking) : BaseBookingState(booking) {
    public override int Id {
        get => (int)BookingStatusCode.ActiveLate;
        set => throw new NotImplementedException();
    }

    public override string Name => Constants.Active;
    public override string TranslationKey => Constants.BookingStatusTranslationKeyActive;
    
    public override Task ActivateLate() {
        throw new NotImplementedException();
    }

    public override Task Return() {
        this.Booking.Status = new ReturnedState(this.Booking);
        return Task.CompletedTask;
    }

    public override Task Terminate() {
        this.Booking.Status = new ForceTerminatedState(this.Booking);
        return Task.CompletedTask;
    }
}