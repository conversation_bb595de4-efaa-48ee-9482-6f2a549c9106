using Cartrack.AppHost;
using Cartrack.EFCore.Models.TfmsCustom;
using Cartrack.Fleet.Booking.Domain.Common;
using Cartrack.Fleet.Booking.Domain.Common.Rules;
using Cartrack.Fleet.Booking.Domain.Scdf.Rules;
using Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf;
using Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf;
using Cartrack.Fleet.Booking.IO.Sql;
using Cartrack.Fleet.Common.IO.Sql;
using Microsoft.EntityFrameworkCore;

namespace Cartrack.Fleet.Booking.Domain.Scdf;

public interface IVehicleBookingBuilder {
    Task<VehicleBookingBase?> Build(long bookingId);
}
public class ScdfVehicleBookingBuilder : VehicleBookingBuilder {
    private ScdfBookingAdditionalInfo? _additionalInfo;

    public ScdfVehicleBookingBuilder(AppCtDbContext ctDbContext,
        AppFleetDbContext fleetDbContext,
        AppPoolDbContext poolDbContext,
        AppTfmsCustomDbContext tfmsCustomDbContext) : base(ctDbContext, fleetDbContext, poolDbContext, tfmsCustomDbContext) {
    }

    public override async Task<VehicleBookingBase?> Build(long id) {
        await this.Start(id);
        await this.IncludeVehicle();
        await this.IncludePickupLocation();
        await this.IncludePurpose();
        await this.IncludeCategory();
        await this.IncludeDriver();
        await this.IncludeJourneys();
        await this.IncludeAccessories();
        await this.IncludeAdditionalInfo();
        await this.IncludeVehicleCommander();
        await this.IncludeRequestedBy();
        await this.IncludeApprovedBy();
        await this.IncludeRejectedBy();
        return this.VehicleBooking;
    }

    public override VehicleBookingBase Create() {
        return new ScdfVehicleBooking();
    }

    public override async Task Start(long bookingId) {
        await base.Start(bookingId);
        this._additionalInfo = await this._tfmsCustomDbContext.ScdfBookingAdditionalInfos
            .Where(info => info.BookingId == this._booking.BookingId)
            .FirstOrDefaultAsync();
    }

    public async Task IncludeAdditionalInfo() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Get vehicle commander

        if (this._additionalInfo != null && !string.IsNullOrEmpty(this._additionalInfo.VehicleCommanderClientUserId)) {
            var clientUser = await this._fleetDbContext.ClientUsers
                .Where(cu => cu.ClientUserId == this._additionalInfo.VehicleCommanderClientUserId)
                .Select(cu => new ClientUserRecord(cu.ClientUserId, cu.UserName))
                .FirstOrDefaultAsync();
            if (clientUser is not null)
                this._clientUsers.Add(clientUser);
        }

        // Adding other additional info for scdf booking
        if (this._additionalInfo != null) {
            this.VehicleBooking!.RequestedForClientUserId = this._additionalInfo.RequestedForClientUserId;
            this.VehicleBooking.LocationType = this._additionalInfo.LocationType;
            this.VehicleBooking.DriverType = this._additionalInfo.DriverType;
            this.VehicleBooking.VehicleCommanderType = this._additionalInfo.VehicleCommanderType;
            this.VehicleBooking.EquipmentType = this._additionalInfo.EquipmentType;
            this.VehicleBooking.NumberOfPassengers = this._additionalInfo.PassengerCount;
        }
    }

    public Task IncludeVehicleCommander() {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));

        // Set vehicle commander
        if (this._additionalInfo != null && !string.IsNullOrEmpty(this._additionalInfo.VehicleCommanderClientUserId)) {
            this.IncludeVehicleCommander(this._additionalInfo.VehicleCommanderClientUserId);
        }

        return Task.CompletedTask;
    }
    public Task IncludeVehicleCommander(string vehicleCommanderClientUserId) {
        Requires.NotNull(this.VehicleBooking, nameof(this.VehicleBooking));
        Requires.NotNullOrEmpty(vehicleCommanderClientUserId, nameof(vehicleCommanderClientUserId));

        // Set vehicle commander
        if (this._additionalInfo != null) {
            this.VehicleBooking!.VehicleCommander = new ClientUser {
                ClientUserId = vehicleCommanderClientUserId,
                Username = this._clientUsers.FirstOrDefault(d => d.ClientUserId == this._additionalInfo.VehicleCommanderClientUserId)?.UserName ?? ""
            };
        }

        return Task.CompletedTask;
    }



    public async Task IncludeBookingApprovalRules(long userId, IBookingRuleRepository ruleRepository) {
        foreach (var r in await GetBookingApprovalRules(userId, ruleRepository)) {
            if (r.IsEnabled == true) {
                this.VehicleBooking!.PreConditions.Add(r);
            }
        }

        return;

        async Task<List<IBookingRule>> GetBookingApprovalRules(long userId, IBookingRuleRepository ruleRepository) {
            var (userBookingRule, bookingSettings) = await ruleRepository.GetBookingSettingRules(userId);

            List<IBookingRule> bookingRule = [];
            foreach (var r in userBookingRule) {
                switch (r.BookingRuleId) {
                    case Constants.BookingRuleCodeCheckDriverLicenseClass:
                        bookingRule.Add(new MustHavePdpLicenseRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeCheckDriverSpecialLicense:
                        bookingRule.Add(new MustHaveQdlLicenseRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeIsDriverRequired:
                        bookingRule.Add(new MustHaveDriverRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeMaximumBookingTime:
                        bookingRule.Add(new MustBeLessThanMaxDurationRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                    case Constants.BookingRuleCodeBookInAdvanceBy:
                        bookingRule.Add(new MustBeLessThanMaxDaysInAdvanceRule(bookingSettings) { IsEnabled = r.Status });
                        break;
                }
            }

            //ADD THE SCDF-specific rules
            bookingRule.Add(new MustHaveDescriptionWhenPurposeIsOthers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveRequestedForWhenBookingForOtherParty(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePassengers(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHavePickupLocation(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidJourneys(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveValidBookingTime(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new MustHaveVehicleRule(bookingSettings) { IsEnabled = true });
            bookingRule.Add(new IsVehicleInCommonPoolRule(bookingSettings) { IsEnabled = true });
            return bookingRule;
        }
    }

    // private async Task  UpdateBookingDetails(ScdfActivateBookingRequest request , ScdfBookingRepository repo, IBookingRuleRepository ruleRepo) {
    //     
    //     
    //     var booking = new ScdfVehicleBooking {
    //         BookingId = request.BookingId,
    //         VehicleId = request.VehicleId,
    //         BookingStatusId = BookingStatusCode.Active,
    //         StartDate = DateTime.SpecifyKind(request.PickUpTime, DateTimeKind.Utc),
    //         EndDate = getCurrentBooking.EndDate,
    //         PickupTime = DateTime.SpecifyKind(request.ActualPickUpTime, DateTimeKind.Utc),
    //         Status = getCurrentBooking!.Status,
    //         UpdatedDate = DateTime.UtcNow,
    //         VehicleCommanderClientUserId = request.VehicleCommanderClientUserId,
    //         RequestClientDriverId = request.ClientDriverId,
    //         ActivatedClientUserId = request.ActivatedClientUserId,
    //         ActivationTrigger = new ManualActivationTrigger()
    //     };
    //     
    //     booking.IsBookingTimeConflicting = await this.IsBookingTimeAvailable(booking);
    //
    //     foreach (var r in await this.GetScdfBookingRules(userId)) {
    //         if (r.IsEnabled == true) {
    //             booking.PreConditions.Add(r);
    //         }
    //     }
    //     
    //     await booking.Validate();
    //
    //     return booking;
    // } 
    public async Task UpdateBookingDetails(ScdfApproveBookingRequest request, ScdfBookingRepository repo, IBookingRuleRepository ruleRepo) {
        var journeys = request.Journeys
            .Select((r, journeyOrder) => new Journey {
                LocationReference = new JourneyLocation { Type = r.Type, Value = r.Value },
                Order = journeyOrder + 1,
            })
            .ToList();

        var booking = (ScdfVehicleBooking)this.VehicleBooking!;
        await this.IncludeJourneys(journeys);
        await this.IncludeVehicle(request.VehicleId);
        await this.IncludePurpose(request.BookingPurposeId);
        this.VehicleBooking!.BookingPurposeDescription = request.BookingPurposeDescription;
        booking.BookingVehicleTypeId = request.BookingVehicleTypeId;
        booking.StartDate = DateTime.SpecifyKind(request.StartTs, DateTimeKind.Utc);
        booking.EndDate = DateTime.SpecifyKind(request.EndTs, DateTimeKind.Utc);
        await this.IncludeDriver(request.RequestClientDriverId!);

        if (!string.IsNullOrWhiteSpace(request.VehicleCommanderClientUserId))
            await this.IncludeVehicleCommander(request.VehicleCommanderClientUserId);

        await this.IncludePickupLocation(request.PickupSiteLocationId);
        booking.ReturnSiteLocationId = (request.JourneyType == JourneyType.Return ? request.PickupSiteLocationId : 0);
        //UpdatedDate = null,
        booking.RequestedForClientUserId = request.RequestedForClientUserId;

        var approvedBy = new List<ClientUser> {
            new ClientUser {
                ClientUserId = "", Username = "SCDF00001"
            }
        };

        booking.ApprovedBy = approvedBy;
        //RejectedBy = null,
        booking.ActivationTrigger = null;
        booking.Remarks = request.Remarks ?? "";
        booking.NumberOfPassengers = request.NumberOfPassengers;
        booking.EquipmentType1 = request.EquipmentType;
        booking.Accessories = request.Accessories ?? null;
        booking.JourneyType = request.JourneyType;

        // Equipment Attachment 
        booking.EquipmentAttachmentIds = request.EquipmentAttachmentIds; // for existing attachment
        booking.EquipmentAttachments = request.EquipmentAttachments; // for new attachments

        await this.IncludeBookingApprovalRules(request.UserId, ruleRepo);
        booking.IsBookingTimeConflicting = await IsBookingTimeAvailable();
        booking.IsBookingPurposeOthersRequired = await IsBookingPurposeOthersRequired();
        return;

        async Task<bool> IsBookingTimeAvailable() {
            bool result = await repo.IsBookingTimeConflicting(booking);
            return result;
        }

        async Task<bool> IsBookingPurposeOthersRequired() {
            bool result = await repo.IsBookingPurposeOthersRequired(booking);
            return result;
        }
    }
}