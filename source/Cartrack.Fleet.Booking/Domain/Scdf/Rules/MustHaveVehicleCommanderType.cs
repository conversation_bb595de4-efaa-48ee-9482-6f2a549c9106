﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveVehicleCommanderType(BookingSettings settings) : IBookingRule {
    public string Name => nameof(MustHaveVehicleCommanderType);
    public required bool? IsEnabled { get; init; } = true;

    public string Description { get; } =
        "Invalid 'vehicle commander type' specified. `VehicleCommanderType` should be filled.";

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = scdfBooking.VehicleCommanderType1 is not null;

        // switch (scdfBooking.VehicleCommanderType1) {
        //     case VehicleCommanderType.Self:
        //     case VehicleCommanderType.Specific:
        //         break;
        // }

        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}