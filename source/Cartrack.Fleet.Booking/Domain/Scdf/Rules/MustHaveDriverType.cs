﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHaveDriverType(BookingSettings settings) : IBookingRule {
    const string InvalidDriverType = "Invalid 'driver type' specified. `DriverType` should be filled.";

    public string Name => nameof(MustHaveDriverType);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = InvalidDriverType;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = scdfBooking.DriverType1 is not null;

        // switch (this.DriverType1) {
        //     case Cartrack.Fleet.Booking.Domain.Common.DriverType.Specific:
        //         //If DriverType == Specific, Driver is required and should be different from the RequestedBy
        //         break;
        //     case Cartrack.Fleet.Booking.Domain.Common.DriverType.SelfDrive:
        //         //If DriverType == SelfDrive, Driver is the same as the RequestedBy (the one making the booking)
        //         //if(this.RequestClientDriverId)
        //         break;
        // }

        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}