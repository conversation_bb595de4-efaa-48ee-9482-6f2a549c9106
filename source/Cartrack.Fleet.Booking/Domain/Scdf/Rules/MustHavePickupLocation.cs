﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain.Scdf.Rules;

public class MustHavePickupLocation(BookingSettings settings) : IBookingRule {
    const string PickupLocationRequired = "Pickup location is required";
    public string Name => nameof(MustHavePickupLocation);
    public required bool? IsEnabled { get; init; } = true;
    public string Description { get; } = PickupLocationRequired;

    public (bool, string) Execute(IBooking booking) {
        var scdfBooking = (ScdfVehicleBooking)booking;
        var pass = scdfBooking.PickupLocation?.Id is > 0;
        return (pass, pass ? string.Empty : $"Validation failed. {this.Description}");
    }
}