﻿using Cartrack.Fleet.Booking.Domain.Common;

namespace Cartrack.Fleet.Booking.Domain;

public interface IBooking {
    long BookingId { get; }
    IBookingState Status { get; set; }
    BookingStatusCode BookingStatusId { get; set; }
    DateTime StartDate { get; }
    DateTime EndDate { get; }
    string? Description { get; }
    public RequestPurpose Purpose { get; set; }
    IBookingTarget Target { get; }
    DateTime CreatedDate { get; }
    DateTime? UpdatedDate { get; }
    BookingType Type { get; }
    Driver.Domain.Common.Driver? RequestedClientDriverId { get; }
    ClientUser? RequestedClientUserId { get; set; }
    ClientUser RequestedBy { get; }
    DateTime RequestedDate { get; }
    IList<ClientUser> ApprovedBy { get; }
    IList<ClientUser> RejectedBy { get; }
    IList<IBookingRule> PreConditions { get; }
    IBookingActivationTrigger ActivationTrigger { get; }
    BookingType BookingType { get; set; }
    string? Remarks { get; }

    // Vehicle Booking
    long? VehicleId { get; set; }
    Common.Driver Driver { get; set; }
    long? BookingVehicleTypeId { get; set; }
    bool IsVehicleCommonPoolRuleAllowed{get;set;}
    Task Validate();
}