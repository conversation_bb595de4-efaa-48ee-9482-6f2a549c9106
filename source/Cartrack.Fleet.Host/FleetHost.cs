﻿using Cartrack.AppHost.WorkerHosting;
using Cartrack.Fleet.Accessory;
using Cartrack.Fleet.Audit;
using Cartrack.Fleet.Auth;
using Cartrack.Fleet.Booking;
using Cartrack.Fleet.Driver;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.Common.IO.Sql;
using Cartrack.Fleet.Vehicle;
using Cartrack.Fleet.User;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Minio;
using Minio.AspNetCore;
using Scalar.AspNetCore;
using System.Net;
using Cartrack.Fleet.Host.Middleware;
using Cartrack.Fleet.License;
using Microsoft.AspNetCore.Authentication;

namespace Cartrack.Fleet.Host;

public class FleetHost(string[] args, bool autoShutdown = true, uint healthCheckFrequencySec = 30)
    : MultiWorkerHost(args, autoShutdown, healthCheckFrequencySec) {
    public override Task OnShutdown() {
        return Task.CompletedTask;
    }

    public override Task OnStartup(CancellationToken token) {
        return Task.CompletedTask;
    }

    protected override void BuildHost(CancellationToken token) {
        var appSetting = new AppSettings(this.EnvVars);
        var env = this.EnvVars;

        var builder = this.Builder;

        builder.WebHost.ConfigureKestrel((context, options) => {
            options.Listen(IPAddress.Any, appSetting.Port, listenOptions => {
                listenOptions.Protocols = HttpProtocols.Http1;
            });

            options.Limits.MaxRequestBodySize = 500 * 1024 * 1024; // 500 MB
            options.Limits.MinRequestBodyDataRate = null;
            options.Limits.MaxRequestBufferSize = null; //unlimited
        });

        builder.Services.Configure<FormOptions>(x => {
            x.ValueLengthLimit = int.MaxValue;
            x.MultipartBodyLengthLimit = int.MaxValue; // I
        });

        builder.Services.AddCors(options => {
            options.AddPolicy("AllowAllOrigins", policy => {
                policy.AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
        });

        //this.AddWorker<JobWorker>();
        AuthStartup.Register(builder.Services, appSetting);
        AuditStartup.Register(builder.Services, appSetting);
        BookingStartup.Register(builder.Services, appSetting);
        AccessoryStartup.Register(builder.Services, appSetting);
        DriverStartup.Register(builder.Services, appSetting);
        VehicleStartup.Register(builder.Services, appSetting);
        UserStartup.Register(builder.Services, appSetting);
        LicenseStartup.Register(builder.Services,appSetting);
            
        
        builder.Services.AddScoped<Cartrack.Fleet.Common.Domain.IJwtService, Cartrack.Fleet.Common.Domain.JwtService>();
        builder.Services.AddScoped<Cartrack.Fleet.Common.Domain.IRsaKeyService, Cartrack.Fleet.Common.Domain.RsaKeyService>();
        builder.Services.AddScoped<Cartrack.Fleet.Common.IO.FileSystem.IFileIo, Cartrack.Fleet.Common.IO.FileSystem.FileIo>();

        builder.Services.AddAntiforgery();
        builder.Services.AddSingleton<AppSettings>(_ => appSetting);
        builder.Services.AddSingleton<IMinioClient>(sp => {
            var settings = appSetting;
            return new MinioClient()
                .WithEndpoint(settings.MinIoUrl, settings.MinIoPort)
                .WithCredentials(settings.MinIoKey, settings.MinIoSecret)
                .WithSSL(true)
                .Build();
        });
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddEntityFrameworkNpgsql();
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddDbContext<AppTfmsCustomDbContext>();
        builder.Services.AddDbContext<AppFleetDbContext>();
        builder.Services.AddDbContext<AppCtDbContext>();
        builder.Services.AddDbContext<AppPoolDbContext>();
        builder.Services.AddSwaggerGen(options => {
            options.SwaggerDoc("v1",
                new OpenApiInfo {
                    Title = "Extended Fleet Services",
                    Version = "v1",
                    Description = "Extended Fleet Services API"
                });
        });
        //builder.Services.AddAuthentication("CustomJwtScheme")
        //    .AddScheme<AuthenticationSchemeOptions, CustomJwtAuthenticationHandler>("CustomJwtScheme", null);

        base.BuildHost(token);

        var app = (WebApplication)this.Host;
        //app.UseMiddleware<FileDownloadMiddleware>();

        if (app.Environment.IsDevelopment()) {
            app.UseDeveloperExceptionPage();
            app.UseSwagger(options => {
                options.RouteTemplate = "/openapi/{documentName}.json";
            });
            app.MapScalarApiReference();
        }

        //app.UseHttpsRedirection();
        //app.UseAuthentication();
        //app.UseJwtAuthentication();
        //app.UseAuthorization();
        app.UseCors("AllowAllOrigins");
        app.MapControllers();
        app.UseWhen(
            context => !context.Request.Path.StartsWithSegments("/auth/login") && !context.Request.Path.StartsWithSegments("/auth/refresh-token") && !context.Request.Path.StartsWithSegments("/scalar"),
            appBuilder => appBuilder.UseMiddleware<RequestJwtAuthMiddleware>()
        );
        app.Run();
    }
}