﻿using Cartrack.Fleet.Auth.Domain;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.Host.Middleware;
public class RequestJwtAuthMiddleware {
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestJwtAuthMiddleware> _logger;

    public RequestJwtAuthMiddleware(RequestDelegate next, ILogger<RequestJwtAuthMiddleware> logger) {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext httpContext, IServiceProvider serviceProvider) {
        var token = httpContext.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

        if (string.IsNullOrEmpty(token)) {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            await httpContext.Response.WriteAsync("Missing token");
            return;
        }

        if (token != null) {
            var jwtService = serviceProvider.GetRequiredService<IJwtService>();

           var principal = jwtService.GetPrincipalFromToken(token);

            if (principal == null) {
                httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await httpContext.Response.WriteAsync("Unauthorized");
                return;
            }

            httpContext.User = principal;
           
        }
        
        await _next(httpContext);
    }
}
