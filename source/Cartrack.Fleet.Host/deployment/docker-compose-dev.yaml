services:
  tfms-services-api:
    container_name: tfms-services-api
    restart: always
    image: as-docker-registry.cartrack.com/cartrack.tfms.services/api:TAG_PLACEHOLDER
    networks:
      - cams
    ports:
      - 5001:8080
      - 6105:6105
    env_file:
      - .env-dev
    volumes:
      - /opt/tfms_services_api/logs:/app/logs
      - /opt/tfms_services_api/certs:/app/certs
    environment:
      # ----------------------
      # Generic AppHost Section
      # ---------------------      
      - APPHOST_TELEMETRY_FREQSEC=300
      - APPHOST_LOG_LEVEL=error
      - APPHOST_SERVICE_NAME=Tfms-Service-Api
      - APPHOST_API_PORT=5001
      - HEALTH_CHECK_EVERY_SECS=600
      - APPHOST_TRACE_ENABLED=True
      - APPHOST_TRACE_SERVER=alerts-dev.cartrack.com
      - APPHOST_TRACE_PORT=7777
      - PORT=6105
      - CORS_ENABLED_ALLOWED_URL=http://localhost:8080,https://localhost:8081,https://crm-me.karooooo.com,
      - CACHE_EXPIRY_SECONDS=3600
      - ALLOWED_HOSTS=*
      - TOKEN_EXPIRY_IN_SECONDS=3600
      - ISSUER=Cartrack
      - USE_IN_MEMORY_REPOSITORY=False
      - IS_RSA_JWT_TOKEN_VALIDATION=False
      - CONNECTION_STRING=Host=db-dev-tfms.cartrack.com;Port=5432;Persist Security Info=True;Password=masterkey;Username=SYSDBA;Database=ct_fleet;
      - TFMS_ENCRYPTION_CYPHER=AES-256-CBC
      - TFMS_ENCRYPTION_KEY=vPWXWxv4rJ7iFQZhnQfq9z1jaO748Ps2
      - TFMS_ENCRYPTION_IV=X4Vwegf5FyXp2MSo
      - AUTH_REFRESH_TOKEN_VALIDITY_DAYS=7
      - AUTH_TOKEN_VALIDITY_MINUTES=1440
      - AUTH_AUDIENCE=tfms-dev
      - AUTH_ISSUER=tfms-dev
      - IS_VALIDATING_PASSWORD=true
      - MAIN_USER=scdf00001,spf000001
      
networks:
  cams:
    external: true