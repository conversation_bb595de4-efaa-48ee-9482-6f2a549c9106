﻿{
  "profiles": {
    "Cartrack.Fleet.Host": {
      "commandName": "Project",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "APPHOST_TELEMETRY_FREQSEC": "5000",
        "APPHOST_LOG_LEVEL": "info",
        "APPHOST_SERVICE_NAME": "FleetHost",
        "APPHOST_API_PORT": "5001",
        "APPHOST_TRACE_ENABLED": "False",
        "APPHOST_TRACE_SERVER": "alerts-dev.cartrack.com",
        "APPHOST_TRACE_PORT": "7777",
        "PORT": "5105",
        "CONNECTION_STRING": "HOST=db-dev-tfms.cartrack.com;PORT=5432;USER ID=CAMS_NET;PASSWORD=masterkey;DATABASE=ct_fleet;CommandTimeout=0",
        "CT_CONNECTION_STRING": "HOST=db-dev-tfms.cartrack.com;PORT=5432;USER ID=CAMS_NET;PASSWORD=masterkey;DATABASE=cartrack;CommandTimeout=0",
        "AUTH_REFRESH_TOKEN_VALIDITY_DAYS": "7",
        "AUTH_TOKEN_VALIDITY_MINUTES": "30",
        "AUTH_PRIVATE_KEY_PATH": "/Users/<USER>/Projects/Work/cartrack.fleet.services/source/Cartrack.Fleet.Host/certs/private-key.pem",
        "AUTH_PUBLIC_KEY_PATH": "/Users/<USER>/Projects/Work/cartrack.fleet.services/source/Cartrack.Fleet.Host/certs/public-key.pem",
        "AUTH_ISSUER": "tfms-dev",
        "AUTH_AUDIENCE": "tfms-dev",
        "IS_VALIDATING_PASSWORD": "false",
        "MAIN_USER": "scdf00001,spf000001"
      }
    }
  }
}
