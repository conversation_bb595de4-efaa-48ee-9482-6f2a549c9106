﻿using Cartrack.AppHost;
using Cartrack.Fleet.Common;
using Cartrack.Fleet.License.Domain;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

namespace Cartrack.Fleet.License.IO.Http;

public class ElitesApiClient(AppSettings settings) : IElitesApiClient {
    private readonly HttpClient _client = GetClient(settings);

    public async Task<QdlEnquiryResponseMessage?> GetQdlLicense(ElitesEnquiryRequest request, CancellationToken token) {
        //TODO: Add all the Requires check
        Requires.NotNullOrEmpty(request.RequestHeader.MessageId, nameof(request.RequestHeader.MessageId));

        var resp = await this._client.PostAsJsonAsync(settings.ElitesQdlApiUrl, request, token);
        var qdl = await resp.Content.ReadFromJsonAsync<QdlEnquiryResponseMessage>(token);

        Requires.IsTrue(() => resp.IsSuccessStatusCode, () => $"QDL Enquiry failed with status code {resp.StatusCode}");
        Requires.IsTrue(() => qdl != null, () => $"QDL response is null");
        Requires.IsTrue(() => qdl!.ResponseHeader.MessageId == request.RequestHeader.MessageId, () => $"Request/Response Message IDs should be the same");

        return qdl;
    }

    public async Task<PdpEnquiryResponseMessage?> GetPdpLicense(ElitesEnquiryRequest request, CancellationToken token) {
        //TODO: Add all the Requires check
        Requires.NotNullOrEmpty(request.RequestHeader.MessageId, nameof(request.RequestHeader.MessageId));

        var resp = await this._client.PostAsJsonAsync(settings.ElitesPdpApiUrl, request, token);
        var pdp = await resp.Content.ReadFromJsonAsync<PdpEnquiryResponseMessage>(token);

        Requires.IsTrue(() => resp.IsSuccessStatusCode, () => $"PDP Enquiry failed with status code {resp.StatusCode}");
        Requires.IsTrue(() => pdp != null, () => $"PDP response is null");
        Requires.IsTrue(() => pdp!.ResponseHeader.MessageId == request.RequestHeader.MessageId, () => $"Request/Response Message ID's should be the same");

        return pdp;
    }

    private static HttpClient GetClient(AppSettings settings) {
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

       #if !DEBUG
        var handler = new HttpClientHandler {
            SslProtocols = SslProtocols.Tls12,
            ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => settings.ElitesValidateSslCertificate
        };

       
        handler.ClientCertificates.Add(LoadCertificate());
        var client = new HttpClient(handler) { Timeout = settings.ElitesPostTimeout };
        #endif
        #if DEBUG
        var client = new HttpClient { Timeout = settings.ElitesPostTimeout };
        #endif
        
        client.DefaultRequestHeaders.Clear();
        client.DefaultRequestHeaders.Add("X-IBM-Client-Id", settings.ElitesClientId);
        client.DefaultRequestHeaders.Add("X-IBM-Client-Secret", settings.ElitesClientSecret);
        //client.DefaultRequestHeaders.Add("X-SPF-Timestamp", dateTime.ToString("MM/dd/yyyy HH:mm:ss tt"));
        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        return client;

        X509Certificate LoadCertificate() {
            var certFile = Path.GetFullPath(settings.ElitesX509Certificate);
            if (File.Exists(certFile)) return new X509Certificate2(certFile);

            throw new FileNotFoundException("Unable to find certificate", certFile);
        }
    }
}