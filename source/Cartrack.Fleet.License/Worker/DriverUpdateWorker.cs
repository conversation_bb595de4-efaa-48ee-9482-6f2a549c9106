﻿using Cartrack.AppHost.Channels;
using Cartrack.Fleet.License.Features.GetLicense;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Worker;

public class DriverUpdateWorker(IChannel channel, ILogger<AppHost.WorkerHosting.Worker> logger, bool autoRestartUnlessStopped = false) : AppHost.WorkerHosting.Worker(logger, autoRestartUnlessStopped) {
    public override async Task DoWork(CancellationToken cancellationToken) {
        var ch = channel.GetChannel<GetLicenseResponse>(cancellationToken);
        // while (!cancellationToken.IsCancellationRequested) {
        //     var msg = await ch.Take();
        //     await SaveToDb(msg);
        // }
        
        await foreach (var msg in ch.TakeAll().WithCancellation(cancellationToken))
        {
            await this.SaveToDb(msg);
        }
        
        await Task.Delay(10000);
    }

    private async Task SaveToDb(GetLicenseResponse? msg) {
        //TODO: use efcore to save the PDP and QDL to DB
        
    }
}