﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.Domain;

public class RestrictedLicenceItem {
    [JsonPropertyName("issueBy")] public string IssueBy { get; set; } = string.Empty;

    [JsonPropertyName("issueDate")] public string IssueDate { get; set; } = string.Empty;

    [JsonPropertyName("expiryDate")] public string ExpiryDate { get; set; } = string.Empty;

    [JsonPropertyName("validity")] public string Validity { get; set; } = string.Empty;

    [JsonPropertyName("class")] public string Class { get; set; } = string.Empty;
}