﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.Domain;

public class QdlConversionItem {
    [JsonPropertyName("qdlSerialNo")] public string QdlSerialNo { get; set; } = string.Empty;

    [JsonPropertyName("convertFrom")] public string ConvertFrom { get; set; } = string.Empty;

    [JsonPropertyName("convertDate")] public string ConvertDate { get; set; } = string.Empty;

    [JsonPropertyName("classes")] public string Classes { get; set; } = string.Empty;

    [JsonPropertyName("foreignLicenceNo")] public string ForeignLicenceNo { get; set; } = string.Empty;

    [JsonPropertyName("countryOfIssue")] public string CountryOfIssue { get; set; } = string.Empty;
}