﻿using System.Text.Json.Serialization;

namespace Cartrack.Fleet.License.Domain;

public class ResponseHeaderData {
    [JsonPropertyName("messageID")] public string MessageId { get; set; } = string.Empty;

    [JsonPropertyName("generationDateTime")]
    public string GenerationDateTime { get; set; } = string.Empty;

    [JsonPropertyName("status")] public string Status { get; set; } = string.Empty;
}