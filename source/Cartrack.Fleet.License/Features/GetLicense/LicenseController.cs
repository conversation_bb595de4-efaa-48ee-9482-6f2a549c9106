using Cartrack.Fleet.License.Features.GetLicense;
using Microsoft.AspNetCore.Mvc;

// ReSharper disable once CheckNamespace
namespace Cartrack.Fleet.License.IO.Http;

public partial class LicenseController {
    
    [HttpGet]
    [Route("driver/{clientDriverId}")]
    public async Task<IActionResult> GetLicense(string clientDriverId) {
        var resp = await mediator.Send(new GetLicenseRequest(clientDriverId));
        return Ok(resp);
    }
}