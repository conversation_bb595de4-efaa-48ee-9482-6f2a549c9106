﻿using Cartrack.AppHost.Channels;
using Cartrack.Fleet.License.Domain;
using Cartrack.Fleet.License.IO.Http;
using Cartrack.Fleet.License.IO.Sql;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Cartrack.Fleet.License.Features.GetLicense;

public class GetLicenseHandler(IChannel channel, IElitesApiClient elitesClient, IClientDriverRepository driverRepo, ILogger<GetLicenseHandler> logger)
    : IRequestHandler<GetLicenseRequest, GetLicenseResponse> {
    public async Task<GetLicenseResponse> Handle(GetLicenseRequest request, CancellationToken cancellationToken) {
        //1. Fetch QDL and PDP from Elites
        //2. Store in TFMS DB

        var pdp = await elitesClient.GetPdpLicense(ElitesEnquiryRequest.Create(request.ClientDriverId), cancellationToken);
        var qdl = await elitesClient.GetQdlLicense(ElitesEnquiryRequest.Create(request.ClientDriverId), cancellationToken);

       
        
        //TODO: In the background, Store in TFMS DB
        //throw new NotImplementedException();
 
        var response = new GetLicenseResponse(IsLicenseValid(qdl, pdp), qdl, pdp);
        var ch = channel.GetChannel<GetLicenseResponse>(cancellationToken);
        await ch.Write(response);
        return response;
    }

    private static bool IsLicenseValid(QdlEnquiryResponseMessage? qdl, PdpEnquiryResponseMessage? pdp) {
        //TODO: Implement validation logic
        return false;
    }
}