﻿using Cartrack.AppHost;

namespace Cartrack.Fleet.Common;

public class AppSettings(IEnvVars env) {
    public int Port { get; set; } = env.Get("PORT", 5105);
    public string ConnectionString { get; set; } = env.Get("CONNECTION_STRING", "")!;
    public int RefreshTokenValidityDays { get; set; } = env.Get("AUTH_REFRESH_TOKEN_VALIDITY_DAYS", 7);
    public int TokenValidityMinutes { get; set; } = env.Get("AUTH_TOKEN_VALIDITY_MINUTES", 30);
    public string PrivateKeyPath { get; set; } = env.Get("AUTH_PRIVATE_KEY_PATH", "")!;
    public string PublicKeyPath { get; set; } = env.Get("AUTH_PUBLIC_KEY_PATH", "")!;
    public string Issuer { get; set; } = env.Get("AUTH_ISSUER", "")!;
    public string Audience { get; set; } = env.Get("AUTH_AUDIENCE", "")!;
    public bool IsValidatingPassword { get; set; } = env.Get("IS_VALIDATING_PASSWORD", false)!;
    public string MainUser { get; set; } = env.Get("MAIN_USER", "scdf00001,spf000001")!;
    public int UserId { get; set; } = env.Get("USER_ID", 302737);
    public string TfmsEncryptionKey { get; set; } = env.Get("TFMS_ENCRYPTION_KEY", "vPWXWxv4rJ7iFQZhnQfq9z1jaO748Ps2")!;
    public string TfmsEncryptionIv { get; set; } = env.Get("TFMS_ENCRYPTION_IV", "X4Vwegf5FyXp2MSo")!;

    public string MinIoUrl { get; set; } = env.Get("MINIO_URL", "web-dev-tfms.cartrack.com")!;
    public int MinIoPort { get; set; } = env.Get("MINIO_PORT", 9000)!;
    public string MinIoKey { get; set; } = env.Get("MINIO_KEY", "Yn4bSIdqKlCEKvjtkm0R")!;
    public string MinIoSecret { get; set; } = env.Get("MINIO_SECRET", "90GMVGwn6t7EDUBPlbyKHgXpkR2MM0CpMJZ6suSK")!;
    public string MinIoBucket { get; set; } = env.Get("MINIO_BUCKET", "fleet-tfms")!;
    
    public bool ElitesValidateSslCertificate { get; set; } = env.Get("ELITES_VALIDATE_SSL_CERT", true);
    public TimeSpan ElitesPostTimeout { get; set; } = TimeSpan.Parse(env.Get("ELITES_POST_TIMEOUT", "00:00:30")!);
    public string ElitesClientId { get; set; } = env.Get("ELITES_CLIENT_ID", string.Empty)!;
    public string ElitesClientSecret { get; set; } = env.Get("ELITES_CLIENT_SECRET", string.Empty)!;
    public string ElitesX509Certificate { get; set; } = env.Get("ELITES_X509_CERT", "cert/client_cert.pfx")!;
    public Uri ElitesQdlApiUrl { get; set; } = new(env.Get("ELITES_QDL_API_URI", "http://localhost:9091/qdl")!);
    public Uri ElitesPdpApiUrl { get; set; } = new(env.Get("ELITES_PDP_API_URI", "http://localhost:9091/pdp")!);
}