﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Cartrack.Fleet.Common;

public static class BaseResponseExtensions {
    private static readonly JsonSerializerOptions Options = new() {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase, NumberHandling = JsonNumberHandling.AllowNamedFloatingPointLiterals
    };

    public static ActionResult<T> ToContentResult<T, V>(this T resp, HttpContext context) where T : BaseResponse<V> {
        var traceId = context.TraceIdentifier;
        var url = context.Request.GetDisplayUrl();

        if (!resp.IsOk) {
            var error = resp.Error!.Message;
            if (resp.IsServerError) {
                var serverError = new BaseError {
                    Detail = $"{error}", Status = StatusCodes.Status500InternalServerError.ToString()
                };
                return new ContentResult {
                    StatusCode = StatusCodes.Status500InternalServerError,
                    ContentType = MediaTypeNames.Application.Json,
                    Content = JsonSerializer.Serialize(serverError, Options)
                };
            }

            var clientError =
                new BaseError {
                    Detail = $"{error}", Status = StatusCodes.Status400BadRequest.ToString()
                };
            return new ContentResult {
                StatusCode = StatusCodes.Status400BadRequest,
                ContentType = MediaTypeNames.Application.Json,
                Content = JsonSerializer.Serialize(clientError, Options)
            };
        }

        if (resp.Value is null) {
            var notFoundError =
                new BaseError {
                    Detail = $"Not Found", Status = StatusCodes.Status404NotFound.ToString()
                };
            return new ContentResult {
                StatusCode = StatusCodes.Status404NotFound,
                ContentType = MediaTypeNames.Application.Json,
                Content = JsonSerializer.Serialize(notFoundError, Options)
            };
        }

        var options = new JsonSerializerOptions {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase, WriteIndented = true
        };

        // Add a converter that respects ShouldSerialize methods
        options.Converters.Add(new ShouldSerializeJsonConverter());

        // Return a JsonResult with our custom options
        return new JsonResult(resp, options);
    }
}

public class ShouldSerializeJsonConverter : JsonConverterFactory {
    public override bool CanConvert(Type typeToConvert) {
        // Check if type has any ShouldSerialize methods
        return typeToConvert.GetMethods(BindingFlags.Public | BindingFlags.Instance)
            .Any(m => m.Name.StartsWith("ShouldSerialize") && m.ReturnType == typeof(bool));
    }

    public override JsonConverter CreateConverter(Type typeToConvert, JsonSerializerOptions options) {
        var converterType = typeof(ShouldSerializeConverter<>).MakeGenericType(typeToConvert);
        return (JsonConverter)Activator.CreateInstance(converterType);
    }

    private class ShouldSerializeConverter<T> : JsonConverter<T> {
        public override T Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options) {
            // Default deserialization
            return JsonSerializer.Deserialize<T>(ref reader, options);
        }

        public override void Write(Utf8JsonWriter writer, T value, JsonSerializerOptions options) {
            writer.WriteStartObject();

            foreach (var prop in typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)) {
                // Get the JsonIgnore attribute if it exists
                var ignoreAttr = prop.GetCustomAttribute<JsonIgnoreAttribute>();

                // Check unconditional JsonIgnore first
                if (ignoreAttr != null && ignoreAttr.Condition == JsonIgnoreCondition.Always)
                    continue;

                // Get property value for conditional checks
                var propValue = prop.GetValue(value);

                // Handle conditional JsonIgnore
                if (ignoreAttr != null) {
                    // Skip null values when condition is WhenWritingNull
                    if (ignoreAttr.Condition == JsonIgnoreCondition.WhenWritingNull && propValue == null)
                        continue;

                    // Skip default values when condition is WhenWritingDefault
                    if (ignoreAttr.Condition == JsonIgnoreCondition.WhenWritingDefault) {
                        var isDefault = propValue == null ||
                                        (prop.PropertyType.IsValueType &&
                                         propValue.Equals(Activator.CreateInstance(prop.PropertyType)));
                        if (isDefault)
                            continue;
                    }
                }

                // Check if there's a ShouldSerialize method for this property
                var shouldSerializeMethod = typeof(T).GetMethod($"ShouldSerialize{prop.Name}",
                    BindingFlags.Public | BindingFlags.Instance);

                if (shouldSerializeMethod != null) {
                    // Invoke the ShouldSerialize method
                    bool shouldSerialize = (bool)shouldSerializeMethod.Invoke(value, null);
                    if (!shouldSerialize)
                        continue; // Skip this property
                }

                // Write property name and value
                var propName = options.PropertyNamingPolicy?.ConvertName(prop.Name) ?? prop.Name;
                writer.WritePropertyName(propName);
                JsonSerializer.Serialize(writer, propValue, options);
            }

            writer.WriteEndObject();
        }
    }
}