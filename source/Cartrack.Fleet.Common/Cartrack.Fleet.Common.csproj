﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Cartrack.AppHost" Version="2.5.4" />
      <PackageReference Include="Cartrack.EFCore.Models.CT" Version="1.1.0" />
      <PackageReference Include="Cartrack.EFCore.Models.Fleet" Version="1.3.1" />
      <PackageReference Include="Cartrack.EFCore.Models.TfmsCustom" Version="1.1.4" />
      <PackageReference Include="Cartrack.EFCore.Models.Pool" Version="1.0.8" />
      <PackageReference Include="Minio.AspNetCore" Version="6.0.1" />
      <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
    </ItemGroup>

</Project>
