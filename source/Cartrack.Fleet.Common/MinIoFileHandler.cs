﻿using Minio;
using Minio.DataModel;
using Minio.DataModel.Args;
using Minio.Exceptions;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Cartrack.Fleet.Common;

public class MinIoHandler(IMinioClient minioClient) {
    private readonly string _bucketName = "fleet-tfms";

    //private readonly string _path = "";
    private readonly string minIoTempFolder = "temp";

    public async Task<MinIoResponseResult> TempUploadBase64ImageAsync(string? base64String, string guidFileName,
        string extension) {
        var result = new MinIoResponseResult {
            ImageUrl = "",
            Extension = "",
            ContentType = "",
            ErrorMessage = ""
        };

        string filePath = Path.Combine(this.minIoTempFolder, guidFileName + "." + extension).Replace("\\", "/");
        try {
            byte[] imageBytes = Convert.FromBase64String(base64String);
            using var stream = new MemoryStream(imageBytes);

            // Upload the image to the MinIO bucket
            await minioClient.PutObjectAsync(new PutObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
                .WithStreamData(stream)
                .WithObjectSize(stream.Length)
            );

            Console.WriteLine("temp image is uploaded successfully to server.");
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = $"{minioClient.Config.Endpoint}/{this._bucketName}/{filePath}",
                Extension = extension,
                ContentType = "",
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo Temp Upload Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                Guid = "",
                ImageUrl = "",
                Extension = "",
                ContentType = "",
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> TempDeleteBase64Image(string guidFileName, string extension) {
        var result = new MinIoResponseResult {
        };

        string filePath = Path.Combine(this.minIoTempFolder, guidFileName + "." + extension).Replace("\\", "/");
        try {
            // Delete the image from /temp folder
            await minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
            );

            Console.WriteLine("temp image is deleted successfully from server.");
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = $"{minioClient.Config.Endpoint}/{this._bucketName}/{filePath}",
                Extension = extension,
                ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo Temp File Delete Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                Guid = guidFileName,
                ImageUrl = $"{minioClient.Config.Endpoint}/{this._bucketName}/{filePath}",
                Extension = extension,
                ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> DeleteImageByUrlFullPath(string urlFullPathFileName) {
        MinIoResponseResult result;

        Uri uri = new Uri(urlFullPathFileName);
        string path = uri.AbsolutePath; // "/fleet-tfms/BookingAttachment/2088/Equipment_1747823720348.jpg"

        // Step 2: Remove the bucket path (e.g., "/fleet-tfms/")
        string trimmedPath = path.TrimStart('/'); // "fleet-tfms/BookingAttachment/2088/Equipment_1747823720348.jpg"
        string[] pathParts = trimmedPath.Split('/');

        // Step 3: Skip the first segment (i.e., bucket name) )
        string filePath = string.Join('/', pathParts.Skip(1)); // "BookingAttachment/2088/Equipment_1747823720348.jpg"

        try {
            // Delete the image from filePath
            await minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(filePath)
            );

            Console.WriteLine("image is deleted successfully from server.");
            result = new MinIoResponseResult {
                ImageUrl = $"{urlFullPathFileName}", ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo File Delete Failed from Minio: " + e.Message);
            result = new MinIoResponseResult {
                ImageUrl = $"{minioClient.Config.Endpoint}/{this._bucketName}/{filePath}", ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<MinIoResponseResult> MoveObjectImage(string fromFileNameWithExtension,
        string toFileNameWithPathAndExtension) {
        var result = new MinIoResponseResult {
        };

        string fromFilePath = Path.Combine(this.minIoTempFolder, fromFileNameWithExtension).Replace("\\", "/");

        try {
            ObjectStat stat = await this.GetObjectStat(fromFilePath);
            if (stat.Size == 0) {
                result = new MinIoResponseResult {
                    ErrorMessage = "File is not found in temporary folder. Ignore"
                };
                return result;
            }

            await minioClient.CopyObjectAsync(new CopyObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(toFileNameWithPathAndExtension)
                .WithCopyObjectSource(new CopySourceObjectArgs()
                    .WithBucket(this._bucketName)
                    .WithObject(fromFilePath))
            );

            // Delete the image from /temp folder
            await minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                .WithBucket(this._bucketName)
                .WithObject(fromFilePath)
            );

            Console.WriteLine("Temp Image is moved successfully to {0}.", toFileNameWithPathAndExtension);
            result = new MinIoResponseResult {
                ImageUrl = $"{minioClient.Config.Endpoint}/{this._bucketName}/{toFileNameWithPathAndExtension}", ErrorMessage = ""
            };
        }
        catch (MinioException e) {
            Console.WriteLine("MinIo (bucket: {3}) Temp File Move Failed to {0} : {1}. {2}",
                toFileNameWithPathAndExtension, fromFilePath, e.Message, this._bucketName);
            result = new MinIoResponseResult {
                ImageUrl = "", ErrorMessage = e.Message
            };
        }

        return result;
    }

    public async Task<ObjectStat> GetObjectStat(string fileNamePathWithExtension) {
        ObjectStat stat = await minioClient.StatObjectAsync(new StatObjectArgs()
            .WithBucket(this._bucketName)
            .WithObject(fileNamePathWithExtension));

        return stat;
    }
}

public record MinIoResponseResult {
    public string? Guid;
    public string? ImageUrl;
    public string? Extension;
    public string? ContentType;
    public string? ErrorMessage;
}